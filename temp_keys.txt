 All Plan 
 Already have an account?
 Approved 
 Bulk Action 
 Bulk Actions 
 Clone 
 Delete 
 Flight Ticket type
 has been registered
 has created a plan request
 Listings
 Mark as Draft 
 Mark as Publish 
 Move to Draft 
 Move to Pending 
 Move to Trash 
 Pending 
 plan request has been approved
 plan request has been cancelled
 Publish 
 Recovery 
 Spam 
- Layout 2&3 : Background Image Uploader
- Layout Normal: Background Color - get code in <a href="https://html-color-codes.info" target="_blank">https://html-color-codes.info</a>
- Layout Normal: Background Image Uploader
- Layout Slider: List Item(s)
- Layout Video: Youtube Url
- List Item(s)
- Style 1 : Image Uploader
- Style 1: Background Image Uploader
- Style 2, Style 3 : Icon Class
- Style Slider: List Item(s)
- Style: Background Image Uploader
-- All --
-- All Category --
-- All Location --
-- Bulk Actions --
-- Customer --
-- Default --
-- Event Type --
-- Local Storage --
-- Please Select --
-- Please select --
-- Select --
-- Select Agent --
-- Select Airline --
-- Select Airport from --
-- Select Airport to --
-- Select Attribute --
-- Select Employer --
-- Select field type --
-- Select one --
-- Select seat type --
-- Select Services --
-- Select Size --
-- Select User --
-- Service Type --
-- Status --
-- User --
-- User Type --
-- Vendor --
--All Category --
--Select Filter--
--Select Icon--
, Baths:
, Sq:
:amount
:amount credit added
:count :guest
:count '.$type->name)}}">{{__(':count'.$type->name,['count'=>$inputValue??$minValue])}}</span>
                        </span>
					@endforeach
				</div>
			</div>
		</div>
		<div class="dropdown-menu custom-select-dropdown">
			@foreach($seatType as $type)
				<?php
                $inputName = 'seat_type_'.$type->code;
                $inputValue = $seatTypeGet[$type->code] ?? $minValue;
                ;?>
			
				<div class="dropdown-item-row">
					<div class="label">{{__('Adults :type
:count '.$type->name)}}">{{__(':count'.$type->name,['count'=>$inputValue??$minValue])}}</span>
                        </span>
					@endforeach
				</div>
			</div>
		</div>
		<div class="dropdown-menu select-seat-type-dropdown" >
			@foreach($seatType as $type)
				<?php
				$inputName = 'seat_type_'.$type->code;
				$inputValue = $seatTypeGet[$type->code] ?? $minValue;
				;?>

				<div class="dropdown-item-row">
					<div class="label">{{__('Adults :type
:count '.$type->name)}}">{{__(':count'.$type->name,['count'=>$inputValue??$minValue])}}</span>
                    </span>
                @endforeach
            </div>
        </div>
    </div>
    <div class="searchMenu-guests__field select-seat-type-dropdown shadow-2" data-x-dd="searchMenu-guests" data-x-dd-toggle="-is-active">
        <div class="bg-white px-30 py-30 rounded-4">
            @foreach($seatType as $type)
                <?php
                $inputName = 'seat_type_'.$type->code;
                $inputValue = $seatTypeGet[$type->code] ?? $minValue;
                ?>

                <div class="row y-gap-10 justify-between items-center">
                    <div class="col-auto">
                        <div class="text-15 fw-500">{{__('Adults :type
:count Adults
:count bath
:count bathroom
:count bed
:count boat found
:count boats found
:count car found
:count cars found
:count Child
:count Children
:count Day
:count day
:count days
:count Days
:count enrolled on this course
:count event found
:count events found
:count flight found
:count flights found
:count Guest in maximum
:count Guests in maximum
:count hotel found
:count hotels found
:count hour
:count Hour
:count hours
:count Hours
:count night
:count nights
:count properties found
:count property found
:count review
:count reviews
:count space found
:count spaces found
:count tour found
:count Tours
:count tours found
:duration day
:duration days
:duration Hours
:duration hrs
:duration Minutes
:duration month
:duration months
:duration week
:duration weeks
:duration year
:duration years
:from - :to guests
:from - :to of :total+ :agency available
:from - :to of :total+ :agent available
:from - :to of :total+ :property available
:from - :to of :total+ :review available
:from - :to of :total+ news available
:from - :to of :total+ properties available
:from to :to
:name - :type
:name - reviews from guests
:name has asked for verification
:name has changed to :status
:name has created :services :title
:name has created a Review :review on :title
:name has created new Booking
:name has requested a Credit Purchase : :amount
:name has requested to become a vendor
:name has sent a Enquiry for :title
:name has sent a Payout request
:name has updated the PAID amount on :title
:name send you file
:name send you message: :message
:name to :info
:num :text
:num day
:num days
:num hour
:num hours
:num min
:num mins
:num month
:num months
:num week
:num weeks
:num year
:num years
:number bath
:number bed
:number Boat
:number Boats
:number Car
:number Cars
:number Event
:number Events
:number Flight
:number Flights
:number Hotel
:number Hotels
:number person
:number persons
:number Properties
:number Property
:number Review
:number review
:number reviews
:number Reviews
:number Space
:number Spaces
:number Tour
:number Tours
:number% of guests recommend
:number% of travelers recommend this experience
:slot not available for your selected 
:title has been deleted by :by
:title was updated to :status by :by
:type by :first_name
') }}
                                            @endif

                                            @if ($row->object_model == 'property
":title" has been added to your cart.
"Do you want to delete?"
"Do you want to permanently delete?"
"Do you want to recovery?"
(:number Review)
(:number Reviews)
(:rate_agv out of 5)
[:site_name] An user submitted verification data
[:site_name] New booking has been made
[:site_name] New inquiry has been made
[:site_name] New message
[:site_name] Permanently Delete Account
[:site_name] The booking status has been updated
[:site_name] Verify Register
[:site_name] We updated your verification data
[:site_name] You get new inquiry request
[:site_name] Your service got new booking
[+][country code][subscriber number including area code]
[Author Deleted]
[Deleted]
[Remove]
[Teacher Deleted]
* Require at least one uppercase, one lowercase letter, one number and one symbol.
*Leave it blank if don't use these fields. The end-time must be larger than start-time
/ per day
/ per hour
/day
/mo
/night
/per day
/per hour
&laquo; Previous
#
© 2020 Find House. Made with love.
$row->pickup
1 Adult
1. Content
1. Room Content
1. Seat Content
1.0 & up
10,000+ unique online course list designs
100$
2 columns
2. Locations
2. Pricing
2.0 & up
2021-12-12 00:00:00
2F Authentication
3 columns
3. Attributes
3. Pricing
3.0 & up
4 columns
4. Attributes
4. Availability
4. Ical
4.0 & up
403
404 Page
5
5. Attributes
5. Ical
6. Ical
A fresh verification link has been sent to your email address.
A payout request has been deleted
A payout request has been rejected
A payout request has been rejected:
A payout request has been updated
A payout request has been updated:
A vendor has been submitted a payout request
A vendor has been submitted a payout request:
About
About me
About Text
About this boat
About this car
About this event
About this hotel
About this rental
About this tour
About Us
About Yourself
Accept All
Accept all
Accept necessary
Accept selected
Access denied for user!. Please check your configuration.
Account Number
Action
Actions
Activate
Active
Add
Add Agency
Add Airline
Add Airport
Add Attributes
Add Boat
Add Car
Add Category
Add Coupon
Add Course
Add credit
Add Credit
Add credit for :name
Add custom HTML script before and after the content, like tracking code
Add Event
Add Flight
Add Flight Seat
Add Flights
Add Folder
Add Hotel
Add Iframe
Add iframe lecture
Add item
Add Language
ADD LAYER
Add layer
Add lecture
Add level
Add Location
Add new
Add New
Add new agency
Add new attribute
Add new Boat
Add new boat
Add new Car
Add new car
Add new category
Add new coupon
Add new Coupon
Add new course
Add new Course
Add new Event
Add new event
Add new field
Add new flight
Add new Flight
Add new Hotel
Add new hotel
Add new Hotel Room
Add new level
Add new location
Add new page
Add new permission
Add new Popup
Add new popup
Add new post
Add new Post
Add New Property
Add new property
Add new Property
Add new role
Add new room
Add new seat
Add new seat type
Add new space
Add new Space
Add new subscriber
Add new Template
Add new term
Add new topic
Add new tour
Add new user
Add News
Add note
Add now
Add Page
Add photo
Add Plan
Add Popup
Add prefix Price in Property listing?
Add presentation
Add presentation lecture
Add Property
Add property
Add Reply
Add Room
Add SCORM
Add scorm lecture
Add Seat
Add Seat Type
Add Section
Add someone to your team:
Add Space
Add Subscriber
Add Support
Add Tag
Add Term
Add To Cart
Add to Menu
Add to wishlist
Add Topic
Add Tour
Add user note
Add video
Add video lecture
Additional details
Additional Rooms
Additional Terms & Information
Additional zoom
Address
Address 2
Address line 1
Address Line 1
Address line 2
Address Line 2
Address-Description
Address:
Address2
Adjust
Admin Dashboard
Admin Email
Admin need approve news to be publish
Admin Password
Admin Phone
Administrator
Administrator has :action your PAYOUT request
Administrator has approved your Credit amount
Administrator has updated the PAID amount on :title
Ads Name
Adult
Adults
adults
Adults:
Advance
Advanced
Advanced features
Advanced Filter
Advanced Search
Advanced Settings
After translation. You must re-build language file to apply the change
Agencies
Agencies list
Agencies Settings
Agency
Agency :name
Agency address
Agency contact
Agency Content
Agency created
Agency fail
Agency Information
Agency name
Agency not found!
Agency updated
Agent
agent
Agent Auto Approved?
Agent config for property
Agent contact
Agent Dashboard
Agent only belong one agencies
Agent Profile
Agent Register
Agent Register Form
Agent Role
Agent Settings
Agent Single
Agents
Ages 12+
Ages 2–12
Agree Text Button
Airline
Airline and time
Airline Content
Airline Management
Airline saved
Airline: :name
Airport
Airport Content
Airport From
Airport Management
Airport saved
Airport To
Airport: :name
alert
All
All
                        Type
All :name
All Agencies
All Agency
All Agent
All Agents
All amount will be in main currency
All Attributes
All Boats
All Booking
All Bookings
All Cars
All categories
All Categories
All Category
All Cities
All Contact Submissions
All Coupons
All Course
All Courses
All Enquiries
All Events
All Fields
All Flight
All Flight seat
All Flight seats
All Flights
All Hotels
All Languages
All Levels
All Location
All Menus
All Modules
All news
All News
All Notifications
All Page
All Permission
All photo
All Popups
All Properties
All Replies
All Reply
All Reviews
All rights reserved.
All Roles
All Rooms
All Spaces
All templates
All Templates
All Terms
All text
All the Term's image are same size
All Themes
All Tickets
All tickets
All topics
All Topics
All Tour
All Tours
All Type
All Users
All Verification
Allow customer can send message to the vendor on detail page
Allow customer upload picture to review
Allow review after making Completed Booking?
Allow search engines to show this service in search results?
Allow vendor can add service fee
Allow vendor can change their booking paid amount
Allow vendor can change their booking status
Allowed full day booking
Already have an account
Already have an account?
Already have an account? 
Always available
Amazon S3
Amenities
Amount
Amount due:
Amount: 
An environment name is required.
An user has been submit their verification data.
Animate
Annual
Annual Price
Answer
any
Any
any bath
any bed
Any Category
Any Location
API Key
Api Key
API KEY
API Password
API Secret
Api Secret
Api secret key
API Username
App Debug
App Environment
APP ID
App Log Level
App Name
App Url
Application
Application Console Output:
Application has been successfully installed.
Application\'s database has been successfully updated.
Apply
APPLY
Apply Filters
Apply watermark
Approve
Approved
Approved By
April
Are you want to delete?
Are you want to update now?. Please make sure you backup all your files and database first
Area
Arrival time
Arrival Time 
As an author, you can add other users to your team. People on your team will be able to manage your services.
ASC
Ask a question
Attachment
Attribute
Attribute Content
Attribute name
Attribute saved
Attribute: :name
Attributes
Attributes not found!
Attributes: :name
August
auth.failed
auth.throttle
Author
Author Setting
Auto-approve team member request?
Autocomplete from Gmap Place
Autocomplete from locations
Availability
Availability Boats
Availability Cars
Availability Events
Availability Properties
Availability Rooms
Availability Spaces
Availability Tours
Available for booking?
Available for payout
Available Rooms
Avatar
Avatar Image
Average
avg/person
AWS S3
Back to flight
Back to Homepage
Back to hotel
Background
Background Color
Background Color - get code in <a href="https://html-color-codes.info" target="_blank">https://html-color-codes.info</a>
Background Gradient overlay
Background Image
Background Image Style 4
Background Image Uploader
Background Video
Badge tag
Baggage
Baggage Cabin
Baggage Check in
Balance
Balance: 
Banner Image
Banner Image 1
Banner Image 2
Banner Page
Banner Property
Banner Sub Title
Based on
Bath
bath
Bathroom
Bathrooms
bathrooms
Baths
Baths:
Become a agent
Become a Real Estate Agent
Become a vendor
Become An Expert
Bed
bed
Bedroom
Bedrooms
Beds
beds
Beds:
Before proceeding, please check your email for a verification link.
Benefits
Billed Monthly
Billed Yearly
Billing to:
Biographical
Birthday
Block Download App
Block Plans
Block subscribe settings
Block Teams
Block: Desc
Block: Title
Blocked
Blue Light
Boat
Boat Attributes
Boat by :name
Boat Content
Boat created
Boat created by vendor must be approved by admin
Boat Featured
Boat ID is not available
Boat information
Boat Management
Boat name
Boat not found
Boat not found!
Boat Settings
Boat updated
Boat Video
Boat: Form Search
Boat: List Items
Boat's Location
Boats
Boats Availability
Boats Availability Calendar
Body Script
bold or 400
Bonus 10%
Bonus 15%
Book
Book :number day in advance
Book :number days in advance
Book Desc
Book Image
Book now
Book Now
BOOK NOW
Book Title
Book Url
Book Url Text
Booking
Booking & Enquiry
Booking Buyer Fees
Booking Buyer Fees Options
Booking Calendar
Booking Core :version Installer
Booking Core by
Booking Date
Booking Deposit
Booking Deposit Options
Booking Detail
Booking Details
Booking details has been sent to:
Booking History
Booking ID
Booking ID: #
Booking not found
Booking not found!
Booking Number
Booking Options
Booking Report
Booking Reports
Booking Settings
Booking Statistic
Booking Status
Booking Status Conditions
Booking status does need to be paid
Booking status not valid
Booking Submission
Booking total is zero. Can not process payment gateway!
Booking Type
BookingCore Team
Bookings
Bookings Statistic
Both
Box Shadow
Brand Logo
Brands List
Breadcrumb Section
Break comments into pages
Brightness
Broadcast Driver
Broadcasting, Caching, Session, &amp; Queue
Broker address
Brown
Browse
Bucket
Build
Bulk action
Business name
Business Name
Button Accept All text
Button Link
Button Name
Button Name 2
Button Reject All text
Button save setting text
Button Title
Button Url
Button Url 2
Buy
Buy credits
Buy Now
Buyer Fees
by
BY
BY 
By continuing, you agree to the
By creating an account, you agree to our Terms of Service and Privacy Statement.
Cabin
Cache Driver
Call To Action
Call us
Campaign
Can not add reply
Can not authorize
Can not check availability
Can not connect to update server. Please check again
Can not create vendor message
Can not download update file to folder storage
Can not edit non-local images
Can not get image dimensions
Can not get image size
Can not get update file from server
Can not register
Can not remove!
Can not run data import
Can not un-zip the package
Can not upload file
Can not upload the file
Cancel
Cancellation Policy
Cancelled
Captcha
Car
Car Attributes
Car Blocks
Car by :name
Car Content
Car created
Car created by vendor must be approved by admin
Car Featured
Car information
Car Management
Car name
Car not found
Car not found!
Car Number
Car Price
Car Sale Price
Car Settings
Car updated
Car Video
Car: Form Search
Car: List Items
Car: List Term Items
Car: Term Featured Box
Car's Location
Card Name
Card Number
Carousel Layout
Cars
Cars Availability
Cars Availability Calendar
Categories
Category
Category :name
Category Content
Category created
Category icon
Category Icon Class
Category Limit
Category name
Category saved
Category Title
Category updated
Center
Change booking email header and footer
Change language of your websites
change license info
Change main color, typo ...
Change map provider of your website
Change Password
Change password
Change social login information for your website
Change the first day of week for the calendars
Change your API for pusher here. It will use for chat plugin and notification
Change your checkout page options
Change your config broadcast site
Change your config email site
Change your config vendor system
Change your config vendor team members
Change your email enquiry options
Change your enquiry options
Change your homepage content
Change your invoice page options
Change your options
Check
Check Availability
Check for update
Check In
Check in
Check In - Out
Check in:
Check in/out time
Check out
Check Out
Check out:
Check Permissions
Check Requirements
Check-in
Checkout
Checkout Page
Child
Children
children
Children:
Choose
Choose file
Choose Layout for Mobile app
Choose Price
Choose your pricing plan
City
City or airport
Class
Class (css)
Class Block
Class icon
Class Icon
Class icon featured
Class Wrapper
Classic Environment Editor
Classic Text Editor
Clear All
Clear Cache
Clear Cache for Booking Core
Clear cache success!
Clear Filters
Click button bellow to start adding layer
Click here to exit
click here to request another
Click onto map to place Location address
Client Feedback
Client Message:
Clone
Clone success!
Close
Close window
Closed
Cloud Storage Configs
Cluster
Code
Code icon
Color
Column
Columns
Comments
Commission
Company Agent at 
Company or title
Completed
Config Booking for event
Config Booking for flight
Config Booking for space
Config Broadcast
Config buyer fees for boat
Config buyer fees for car
Config buyer fees for course
Config buyer fees for hotel
Config buyer fees for tour
Config Email
Config for vendor
Config google recapcha for system
Config inbox option
Config Nexmo Driver
Config page detail property of your website
Config page list news of your website
Config page search of your website
Config Phone Administrator
Config register option
Config review for agency
Config review for agent
Config review for boat
Config review for car
Config review for course
Config review for event
Config review for flight
Config review for hotel
Config review for property
Config review for space
Config review for tour
Config sidebar for news
Config Sms
Config tracking system option
Config Twilio Driver
Config user plans page
Config Vendor
Configure Environment
Confirm
CONFIRM BOOKING
Confirm Password
Confirm password
Confirm permanently delete account
Confirmation
Confirmed
Contact
Contact Agent
Contact Banner
Contact Block
Contact Desc
Contact Featured Image
Contact Form
Contact Information
Contact Locations
Contact Now
Contact Page
Contact partner button link
Contact partner button text
Contact partner sub title
Contact partner title
Contact partners
Contact property
Contact Sub Title
Contact sub title
Contact Submissions
Contact title
Contact Title
Contact Us
Contact vendor
Content
Content confirm
Content Email Agent Registered
Content Email Permanently delete account
Content email send to Agent or Administrator when user registered.
Content email send to Customer or Administrator when user registered.
Content email send to Customer or Administrator.
Content email send to Vendor or Administrator when user registered.
Content Email User Forgot Password
Content Email User Registered
Content Email User Verify Registered
Content Email Vendor Registered
Content email verify send to Customer when user registered.
Content email verify send when user permanently deleted.
Content Text
Content:
Context
Continue Apple
Continue Facebook
Continue Google
Contrast
Convert To
Cookie agreement
Cookie agreement config
Cookie preferences
Cookie Settings Modal
Cookie Title
Counter
Country
Coupon
Coupon Amount
Coupon code
Coupon Code
Coupon code is added already!
Coupon code is applied successfully!
Coupon code is not applied to this product!
Coupon code is not applied to your account!
Coupon created
Coupon Management
Coupon Name
Coupon updated
Course
Course Attributes
Course Category
Course Content
Course created
Course Featured
Course Level
Course Management
Course name
Course not found
Course not found!
Course Options
Course Settings
Course updated
Courses
Courses Availability Calendar
Courses: List Items
CPC
Create
Create a Agency
Create a new account?
Create a ticket
Create account
Create Agency
Create Agent
Create an account
Create an account.
Create Boats
Create Booking
Create Cars
Create Coupon
Create Courses
Create Events
Create Flight seat
Create Flights
Create Hotels
Create Listing
Create new menu
Create new template
Create new ticket
Create payout request
Create Properties
Create request
Create Room
Create Spaces
Create ticket
Create Tours
Created at
Created At
Created at: 
Created: :date
Credit
Credit Amount
Credit exchange rate
Credit Options
Credit purchase report
Credit Purchase Report
Credit Purchase Report :count
Credit Purchase Updated Template
Credit want to pay?
Credit: :amount
critical
Crop
Currency
Currency Format
Currency:
Currency: :currency_main
Current Password
Current password is not correct
Current Plan
Current Tab
custom
Custom
Custom Class
Custom Class (optional)
Custom CSS
Custom CSS for :name
Custom CSS for all languages
Custom HTML Description
Custom Logo
Custom Name
Custom Range
Custom Requirement:
Custom Scripts
Custom Scripts for :name
Custom Scripts for all languages
Custom Title
Custom Url
Customer
Customer care available 24/7
Customer Info
Customer Information
Customer information
Customer must book a boat before writing a review?
Customer must book a car before writing a review?
Customer must book a course before writing a review?
Customer must book a event before writing a review?
Customer must book a flight before writing a review?
Customer must book a hotel before writing a review?
Customer must book a space before writing a review?
Customer must book a tour before writing a review?
Customer Name
Customer name
Customer User
CVC
D
Danger
Dark
Dashboard
Dashboard Navigation
Data
Database
Database Connection
Database Host
Database Name
Database Password
Database Port
Database User Name
Date
DATE 
Date approved
Date Create
Date format
Date From-To
Date Information
Date Processed
Date published
Date Published
Date Ranges
Date request
Date Request
Dates are not valid
Day
day
Day of Week
Day: 
Days
Days:
Deactivate
debug
December
Decimal Separator
Default
Default language source do not have any strings
Default language source does not exists
Default language source empty
Default region
Default State
Default: 9
Del
Delete
Delete account
Delete boat success!
DELETE booking
Delete car success!
DELETE Enquiry
Delete event success!
Delete fail!
Delete file
Delete flight success!
Delete hotel success!
Delete lecture successfully!
Delete property success!
Delete room success!
Delete section successfully!
Delete space success!
Delete success!
Delete the file success!
Delete this folder
Delete tour success!
Delete your account
Deleted success!
Deleted successfully!
Deleted!
Deletel
Demo data has been imported
DEMO MODE: can not add data
DEMO MODE: Disable setting update
DEMO MODE: Disable update
DEMO MODE: You are not allowed to do that
DEMO MODE: You can not change password!
DEMO Mode: You can not do this
Departure time
Deposit
Deposit amount
Deposit Amount
Deposit amount + Buyer free
Deposit by :name
Deposit Fomular
Deposit lists
Deposit option amount is not valid
Deposit option credit is not valid
Deposit option is not valid
Deposit Options
Deposit rate
Deposit type
DESC
Desc
Desc (using for slider ver 2)
Desc:
Desc: TP. HCM City
Description
Destinations
Detail
Detail statistics
Details
Development
diff_after_tomorrow
diff_before_yesterday
diff_now
diff_tomorrow
diff_yesterday
Disable boat module
Disable boat module?
Disable car module
Disable car module?
Disable Commission
Disable course module
Disable course module?
Disable event module
Disable event module?
Disable flight module
Disable flight module?
Disable for demo mode
Disable hotel module
Disable hotel module?
Disable Payout Module?
Disable Registration?
Disable space module
Disable space module?
Disable subscribe default
Disable tour module
Disable tour module?
Disable two factor authentication
Disable verification feature
Disable verification feature?
Disable wallet module
Disable Wallet module?
Discount
Discount by number of people
Discount Type
Discounts:
Discover
Discover Link
Discover Title
Display Center Icon
Display Left Icon
Display Name
Display name is a required field
Display Order
Display Type
Display Type in detail service
Distance
Do not have an account?
Do not track these IP address
Do you have a promo code?
Do you want to delete those items?
Do you want to delete?
Do you want to import all demo data?
Do you want to permanently delete?
Do you want to restore?
Does the review need approved by admin?
Don't have an account yet?
Done
Door
Download App
Draft
Duplicated
Duration
Duration (hour)
Duration (minute)
Duration is required
Duration Type
Duration Unit
Duration:
Duration:  :duration_text
Durations
Durations:
E-mail
E-Mail Address
Earn credit
Earning
Earning Statistics
Earning statistics
Earnings
Easy Installation and Setup Wizard.
Edit
Edit  :name
Edit Agency
Edit Agency :name
Edit airline
Edit airport
Edit Boat
Edit Boats
Edit Car
Edit Cars
Edit category
Edit Coupon: :name
Edit Course
Edit Courses
Edit Event
Edit Events
Edit field: :name
Edit Field: :name
Edit Flight
Edit flight_seat
Edit Flights
Edit Hotel
Edit hotel
Edit Hotels
Edit Lecture
Edit Menu:
Edit Page
Edit Popup
Edit post: 
Edit Profile
Edit Properties
Edit Property
Edit Role
Edit room: :name
Edit seat type
Edit Section
Edit section
Edit Space
Edit Spaces
Edit Template:
Edit topic: :name
Edit Tour
Edit Tours
Edit user plan
Edit User: #:id
Edit verification field
Edit: 
Edit: :email
Edit: :name
Edit: #:name
Editor
Effects
Eg: 11:00AM
Eg: 12:00AM
Eg: 5
Eg: 6000km
Eg: Additional Services
Eg: Adults
Eg: bank_transfer
Eg: booking from 22-23, then all days 22 and 23 are full, other people cannot book
Eg: Can I bring my pet?
Eg: fa fa-phone
Eg: gb
Eg: Range
Eg: Service
Eg: service VIP
Eg: Service VIP
Eg: Specialized bilingual guide
Eg: What kind of foowear is most suitable ?
Eg: When and where does the tour end?
Email
email
Email :email exists. Can not register new account with your social email
Email *
Email Address
Email address
Email Ads Click
Email Driver
Email Encryption
Email exists
Email for Admin
Email for Customer
Email Form Address
Email Form Name
Email From Config
Email Header & Footer
Email Host
Email invalidate
Email is Required
Email is required field
Email Password
Email Port
Email Preview
Email Settings
Email Testing
Email to Administrator content
Email to Administrator subject
Email to agent content
Email to agent subject
Email to customer content
Email to Vendor content
Email to vendor content
Email Username
Email verified
Email Verified?
Email:
Email: 
<EMAIL>
emergency
Enable
Enable enquiry for Boat
Enable enquiry for Car
Enable enquiry for Event
Enable enquiry for Hotel
Enable enquiry for Space
Enable enquiry for Tour
Enable enquiry form
Enable extra price
Enable Facebook Login?
Enable featured
Enable Fixed Date
Enable full day booking
Enable Google Login?
Enable guest checkout
Enable Header Sticky
Enable hide
Enable instant booking
Enable mega menu
Enable Multi Languages
Enable Multi User Plans
Enable must verify email when customer registered ?
Enable now
Enable Offline Payment?
Enable Open Hours
Enable Paypal Standard?
Enable Payrexx Checkout?
Enable Paystack gateway?
Enable Person Types
Enable Preload
Enable re-captcha at enquiry form
Enable re-catpcha for enquiry?
Enable reCapcha Booking Form
Enable reCapcha Form
Enable reCapcha Login Form
Enable reCapcha Register Form
Enable ReCaptcha
Enable review system for Agency?
Enable review system for Agent?
Enable review system for Boat?
Enable review system for Car?
Enable review system for Course?
Enable review system for Event?
Enable review system for Flight?
Enable review system for Hotel?
Enable review system for News?
Enable review system for Property?
Enable review system for Space?
Enable review system for Tour?
Enable RTL
Enable Sandbox Mod?
Enable Sandbox Mode
Enable send email to Administrator
Enable send email to Administrator when customer registered ?
Enable send email to Administrator?
Enable send email to customer when customer registered ?
Enable send email to customer?
Enable send email to Vendor
Enable send sms to Administrator when have booking?
Enable send sms to Administrator when update booking?
Enable send sms to Customer when have booking?
Enable send sms to Customer when update booking?
Enable send sms to Vendor when have booking?
Enable send sms to Vendor when update booking?
Enable service fee
Enable Stripe Checkout V2?
Enable Ticket/Guest information
Enable Tracking
Enable Twitter Login?
Enable Two Checkout?
Enable User Plans
Enable?
End Date
End date
End date:
End Time
End time booking
End time booking: :time
Enquiries
Enquiry
Enquiry :name
Enquiry Click
Enquiry Management
Enquiry not found!
Enquiry Report
Enquiry Reports
Enquiry Settings
Enquiry Type
Enter a property name or an address
Enter agency name
Enter Agent Name
Enter agent name
Enter description...
Enter Email
Enter First Name
Enter Keyword
Enter keyword ...
Enter Keywords
Enter Last Name
Enter Password
Enter Phone
Enter promo code
Enter tag
Enter Title
Enter title...
Enter your environment...
Enter Your Messages
Envato username
Environment
Environment Settings
Equipment
error
Error 404 background image
Error 404 banner
Error desc
Error title
Error upload...
Error. You can\'t permanently delete
Event
Event Attributes
Event Blocks
Event by :name
Event Content
Event created
Event created by vendor must be approved by admin
Event Featured
Event information
Event Location
Event Management
Event name
Event not found
Event not found!
Event Price
Event Sale Price
Event Settings
Event snapshot
Event updated
Event Video
Event: Form Search
Event: List Items
Event: Term Feature Box
Events
Events Availability
Events Availability Calendar
Ex: 1
Ex: 100
Ex: 15:00
Ex: 2
Ex: 21:00
Ex: 3
Ex: fa fa-facebook
Example
Example value : 10 or 10.5
Example: 10% commssion. Vendor get 90%, Admin get 10%
Example: 100
Example: *************, **************
Example: 2020
Example: 25km/h
Example: 3
Example: 30m
Example: 4
Example: 5
Example: Auto
Example: Main currency is VND (which does not support by PayPal), you may want to convert it to USD when customer checkout, so the exchange rate must be 23400 (1 USD ~ 23400 VND)
Example: Main currency is VND, and the extra currency is USD, so the exchange rate must be 23400 (1 USD ~ 23400 VND)
Example: Money * Deposit rate = Credit
Excellent
Exchange rate
Exchange Rate
Exchange rate to :name must be specific. Please contact site owner
Exchange rate will be used in checkout page. Example: Credit * Exchange rate = Money
Exclude
Exclude URLs
Excluded
Excludes
Execution Time
Expand All Sections
Expiration
Expiration Month
Expiration Year
Expired
Expiry
Explore
Explore deals, travel guides and things to do in :text
Explore other options
Explore the place
Export
Export to excel
Export url
Exposure
Extra Currency
Extra Info
Extra Price
Extra price name
Extra prices:
Extra Prices:
Facebook
Facebook Client Id
Facebook Client Secret
Facebook Description
Facebook Image
Facebook Title
Failed
False
FAQ List
FAQs
FAQs about
Favicon
Favorites
Fax
Fb
FEATURE
Feature Image
FEATURED
Featured
FEATURED ARTICLE
Featured icon
Featured icon (find icon class in : https://icofont.com/icons)
Featured Image
Featured Listings
Featured Properties
Featured text
Featured Text
Featured Value
Features & Amenities
February
Fee desc
Fee name
Fee:
Field created
Field ID
Field ID 
Field Name
Field not found
Field saved
File
File attachment
File is required
File language source do not have any strings
File language source does not exists
File language source empty
File not found!
file selected
File type are not allowed
File type invalid
File URL
File: :file_name is not write-able. Please contact your hosting provider
files
Filter
FILTER BY
Filter by category
Filter by Category
Filter by Location
Filter by Plan
Filter by Role
Filter Price
Filter Search
Filter:
Filter: 
Filters
Final .env File:
Find Agent
Find Agents
Find out more topics
Find Translations
Find your home
Finished
First item is main item
First name
First Name
First Name: 
First row 2 cards
First row 3 cards
Five Star
Fixed
Fixed dates
Flag Icon
Flight
Flight Attributes
Flight Blocks
Flight by :name
Flight by day
Flight by night
Flight clone was successful
Flight Content
Flight created
Flight created by vendor must be approved by admin
Flight Details
Flight id
Flight information
Flight Management
Flight name
Flight not found
Flight not found!
Flight seat
Flight seat created
Flight seat Management
Flight seat saved
Flight seat updated
Flight Settings
Flight ticket
Flight updated
Flight: :name
Flight: :name :code #:id
Flight: Form Search
Flights
Flip Horizontally
Flip Vertically
Floor Plans
Focus point
Folder
Folder deleted
Folder name exists, please select new one
Folder not found. Please try again
Folder: resources/lang is not write-able. Please contact your hosting provider
Font Family
Font Size
Font Weight
Footer
Footer Content
Footer content left
Footer content right
Footer Info Contact
Footer List Widget
Footer Script
Footer Style
Footer Text Left
Footer Text Right
For buy
For Buy
For rent
For Rent
For Role
For roles
For Roles?
For Sale
FOR SALE
For Sanitary purposes ONLY, although there is a working toilet and shower, we've deactivated the shower and the toliet is for limited use (urine only..pardon the graphic detail!)...
For security, please change your password to continue
Forbidden
Forgot Password
Forgot Password?
Form Search
Form Search All Service
Form Search Fields
Form Sub Title
Form Title
Form Wizard Setup
Format
Forum
forum saved
Forums
Found :count airport(s)
Found :total items
Found :total texts
Founded Time
Four Star
Fr
Free
Frequently asked questions
Friday
from
From
From 
From - To
from :from guests
from :number reviews
From where
Full Book
Full Name
Full refund up to 4 days prior.
Fun Fact
Gallery
Gallery Images
Garage
Garages
Gateway
Gateway 2Checkout
Gateway 2Checkout is one of the best payment Gateway to accept online payments from buyers around the world which allow your customers to make purchases in many payment methods, 15 languages, 87 currencies, and more than 200 markets in the world.
Gear
Gear Shift
General
General info
General Options
General Settings
General Style
Generate
Generate Default JSON Language
get icon in <a href=':link_1' target='_blank'>fontawesome.com</a> or <a href=':link_2' target='_blank'>icofont.com</a>
Get lat - lng in here
Get More Information
Get Updates & More
Gmap API Key
Go Back
Go Back To Homepage
Go back to homepage
Go Back To Homepages
Go Home
Go to home
Go to page :page
Google
Google Client Id
Google Client Secret
Google Cloud Storage
Google Map
Google Map Block
Google reCapcha Options
Grayish Blue
Green
Grid
Grid Cols
Grid Item
Grid Layout
Grid Style
Group Size
Guest
guest
Guest #:number
Guest Checkout
Guest name
Guest reviews
guests
Guests
Guests Information
Guided <code>.env</code> Wizard
H
H1 Font Family
H1,H2,H3 Options
H2 Font Family
H3 Font Family
Happy people number
Happy people text
Head Script
Header
Header & Footer Settings
Header Align
Header Settings
Header Style
height
Hello
Hello :name
Hello administrator
Hello Administrator
Hello!
Here are new contact information:
Here is the message from vendor:
Hi, :name
Hi, :Name
Hi, I'm :name
Hide
Hide
                                    Filter
Hide button scroll down?
Hide Filter
Hide form search service?
Hide in detail service
Hide in filter search
Hide Slider Controls
Home
Homepage
Hotel
Hotel Attributes
Hotel Blocks
Hotel by :name
Hotel Content
Hotel created
Hotel created by vendor must be approved by admin
Hotel Featured
Hotel information
Hotel Management
Hotel name
Hotel not found
Hotel Policies
Hotel Policy
Hotel Price
Hotel rating standard
Hotel Room
Hotel Rules - Policies
Hotel Sale Price
Hotel Settings
Hotel Star
Hotel updated
Hotel Video
Hotel: :name
Hotel: Form Search
Hotel: List Items
Hotels
hour
Hour
Hour: 
Hours
hours
How are you feeling today?
How can I get my license key?
How Can We Help?
How do you want to pay?
How It Works
How many publish services user can post
How much would you like to deposit?
How your customer can contact to you
https://m.me/bookingcore
https://www.bookingcore.co
I already back up all files and database
I confirm that I have read and accepted the privacy policy
I have read and accept the
I have read and accept the <a href=':link' target='_blank'>Terms and Privacy Policy</a>
I have read and accept the Terms and Privacy Policy?
IATA Code
Ical
Icon
Icon Box
Icon class
Icon Class
Icon Class - get class in <a href="https://fontawesome.com/v4/icons/" target="_blank">https://fontawesome.com/v4/icons/</a>
Icon Class - get class in <a href="https://www.flaticon.com" target="_blank">https://www.flaticon.com</a>
Icon code
Icon Image
Icon Marker in Map
ID
If left blank the button will be hidden
If left blank, the total time of the lectures will automatically be calculated
If the regular price is less than the discount , it will show the regular price
If you did not create an account, no further action is required.
If you did not receive the email
If you did not request a password reset, no further action is required.
Iframe google map
Iframe Google Map
Image
Image 1
Image 2
Image Background
Image Editor
Image Text With Counting
Image Upload
Image Uploader
Image Uploader 2
Image Uploader 3
Import
Import Demo Data
Import from IATA
Import new Template
Import Queued
Import Template
Import template ' . @$dataInput['title'] . ' success!
Import url
Important information
In case of main currency does not support by PayPal. You must select currency and input exchange_rate to currency that PayPal support
in minutes
In Response To
Inactive
Inbox System
Include
Include URLs
Included
Included/Excluded
Includes
Info
info
Info Contact
Information
Information Contact
Information of your website for customer and goole
Initial
Input time format, ex: 15:00
Input time format, ex: 21:00
Install
Installation Finished
Installation Log Entry:
installer_messages.environment.classic.back
installer_messages.environment.classic.install
installer_messages.environment.classic.save
installer_messages.environment.classic.templateTitle
installer_messages.environment.classic.title
installer_messages.environment.errors
installer_messages.environment.menu.classic-button
installer_messages.environment.menu.desc
installer_messages.environment.menu.templateTitle
installer_messages.environment.menu.title
installer_messages.environment.menu.wizard-button
installer_messages.environment.success
installer_messages.environment.wizard.form.app_admin_email_placeholder
installer_messages.environment.wizard.form.app_admin_password_placeholder
installer_messages.environment.wizard.form.app_debug_label
installer_messages.environment.wizard.form.app_debug_label_false
installer_messages.environment.wizard.form.app_debug_label_true
installer_messages.environment.wizard.form.app_environment_label
installer_messages.environment.wizard.form.app_environment_label_developement
installer_messages.environment.wizard.form.app_environment_label_local
installer_messages.environment.wizard.form.app_environment_label_other
installer_messages.environment.wizard.form.app_environment_label_production
installer_messages.environment.wizard.form.app_environment_label_qa
installer_messages.environment.wizard.form.app_environment_placeholder_other
installer_messages.environment.wizard.form.app_log_level_label
installer_messages.environment.wizard.form.app_log_level_label_alert
installer_messages.environment.wizard.form.app_log_level_label_critical
installer_messages.environment.wizard.form.app_log_level_label_debug
installer_messages.environment.wizard.form.app_log_level_label_emergency
installer_messages.environment.wizard.form.app_log_level_label_error
installer_messages.environment.wizard.form.app_log_level_label_info
installer_messages.environment.wizard.form.app_log_level_label_notice
installer_messages.environment.wizard.form.app_log_level_label_warning
installer_messages.environment.wizard.form.app_name_label
installer_messages.environment.wizard.form.app_name_placeholder
installer_messages.environment.wizard.form.app_tabs.broadcasting_label
installer_messages.environment.wizard.form.app_tabs.broadcasting_placeholder
installer_messages.environment.wizard.form.app_tabs.broadcasting_title
installer_messages.environment.wizard.form.app_tabs.cache_label
installer_messages.environment.wizard.form.app_tabs.cache_placeholder
installer_messages.environment.wizard.form.app_tabs.mail_driver_label
installer_messages.environment.wizard.form.app_tabs.mail_driver_placeholder
installer_messages.environment.wizard.form.app_tabs.mail_encryption_label
installer_messages.environment.wizard.form.app_tabs.mail_encryption_placeholder
installer_messages.environment.wizard.form.app_tabs.mail_host_label
installer_messages.environment.wizard.form.app_tabs.mail_host_placeholder
installer_messages.environment.wizard.form.app_tabs.mail_label
installer_messages.environment.wizard.form.app_tabs.mail_password_label
installer_messages.environment.wizard.form.app_tabs.mail_password_placeholder
installer_messages.environment.wizard.form.app_tabs.mail_port_label
installer_messages.environment.wizard.form.app_tabs.mail_port_placeholder
installer_messages.environment.wizard.form.app_tabs.mail_username_label
installer_messages.environment.wizard.form.app_tabs.mail_username_placeholder
installer_messages.environment.wizard.form.app_tabs.more_info
installer_messages.environment.wizard.form.app_tabs.pusher_app_id_label
installer_messages.environment.wizard.form.app_tabs.pusher_app_id_palceholder
installer_messages.environment.wizard.form.app_tabs.pusher_app_key_label
installer_messages.environment.wizard.form.app_tabs.pusher_app_key_palceholder
installer_messages.environment.wizard.form.app_tabs.pusher_app_secret_label
installer_messages.environment.wizard.form.app_tabs.pusher_app_secret_palceholder
installer_messages.environment.wizard.form.app_tabs.pusher_label
installer_messages.environment.wizard.form.app_tabs.queue_label
installer_messages.environment.wizard.form.app_tabs.queue_placeholder
installer_messages.environment.wizard.form.app_tabs.redis_host
installer_messages.environment.wizard.form.app_tabs.redis_label
installer_messages.environment.wizard.form.app_tabs.redis_password
installer_messages.environment.wizard.form.app_tabs.redis_port
installer_messages.environment.wizard.form.app_tabs.session_label
installer_messages.environment.wizard.form.app_tabs.session_placeholder
installer_messages.environment.wizard.form.app_url_label
installer_messages.environment.wizard.form.app_url_placeholder
installer_messages.environment.wizard.form.buttons.install
installer_messages.environment.wizard.form.buttons.setup_application
installer_messages.environment.wizard.form.buttons.setup_database
installer_messages.environment.wizard.form.db_connection_failed
installer_messages.environment.wizard.form.db_connection_label
installer_messages.environment.wizard.form.db_connection_label_mysql
installer_messages.environment.wizard.form.db_connection_label_pgsql
installer_messages.environment.wizard.form.db_connection_label_sqlite
installer_messages.environment.wizard.form.db_connection_label_sqlsrv
installer_messages.environment.wizard.form.db_host_label
installer_messages.environment.wizard.form.db_host_placeholder
installer_messages.environment.wizard.form.db_name_label
installer_messages.environment.wizard.form.db_name_placeholder
installer_messages.environment.wizard.form.db_password_label
installer_messages.environment.wizard.form.db_password_placeholder
installer_messages.environment.wizard.form.db_port_label
installer_messages.environment.wizard.form.db_port_placeholder
installer_messages.environment.wizard.form.db_username_label
installer_messages.environment.wizard.form.db_username_placeholder
installer_messages.environment.wizard.form.name_required
installer_messages.environment.wizard.tabs.application
installer_messages.environment.wizard.tabs.database
installer_messages.environment.wizard.tabs.environment
installer_messages.environment.wizard.templateTitle
installer_messages.environment.wizard.title
installer_messages.final.console
installer_messages.final.env
installer_messages.final.exit
installer_messages.final.finished
installer_messages.final.log
installer_messages.final.migration
installer_messages.final.templateTitle
installer_messages.final.title
installer_messages.forms.errorTitle
installer_messages.installed.success_log_message
installer_messages.next
installer_messages.permissions.next
installer_messages.permissions.templateTitle
installer_messages.permissions.title
installer_messages.requirements.next
installer_messages.requirements.templateTitle
installer_messages.requirements.title
installer_messages.title
installer_messages.updater.final.exit
installer_messages.updater.final.title
installer_messages.updater.log.success_message
installer_messages.updater.overview.install_updates
installer_messages.updater.overview.message
installer_messages.updater.title
installer_messages.updater.welcome.message
installer_messages.updater.welcome.title
installer_messages.welcome.message
installer_messages.welcome.next
installer_messages.welcome.templateTitle
installer_messages.welcome.title
Instance name
Instant Booking?
Instructor
Instructor Rating
Instructors
Interdum et malesuada fames
Interdum et malesuada fames ac ante ipsum
Invalid coupon code!
INVOICE
Invoice
Invoice #: :number
Invoice Company Info
Invoice Logo
Invoice Page
Ip
Is Featured?
Is Instant Booking?
Is Required?
Is Sold
Is Vendor
Item
Item not found
Itinerary
January
Job
Job created by vendor must be approved by admin
Join
July
June
Keep original resolution
Keep Yourself Up to Date
Key
Keyword
Keywords
km
Km
Label
Landing
Lang
Language
Language Content
Language created
Language does not exists
Language Management
Language updated
Languages
Laravel Installer
Laravel Installer successfully INSTALLED on 
Laravel Installer successfully UPDATED on 
Laravel Updater
Last 30 Days
Last 7 Days
Last Booking Date
Last build at
Last check for update: :date
Last Month
Last name
Last Name
Last Name: 
Last remodel year
Last reply
Last run: :date
Last saved:
Last update success: :date
Last Updated
Last updated :date_update
Latest Transactions
Latest version available: :version
LAYERS
Layout
Layout 1
Layout 2
Layout Detail
Layout Item Hotel In Page Search
Layout Map Option
Layout Map Position
Layout Map Size
Layout Search
Learn how to get an api key
Learn More
Leave a Reply
Leave A Review
Leave a review
Leave a Review
Leave blank if you allow writing the review with all booking status
Leave blank if you dont need to set minimum day stay option
Leave blank if you dont need to use the min day option
Leave blank to use service title
Lecture created
Lecture name
Lecture name is required
Lecture not found
Lecture updated
Lectures Management
Left
Left ($100)
Left with space ($ 100)
Length
Length Boat
lesson
Lessons
lessons
Let us know who you are
Level
Level :name
Level Content
Level name
Level saved
Levels
License information has been saved
License Key Information
Like
Limit item per Page
Line Height
Line number
Link
Link download
Link Download
Link Map
Link More
Link social
Link Text
Link to
Link to location detail page?
Link Video Ember
Link View on Map
Link:
Linkedin
List
List About
List Agent
List All Service
List App(s) Download
List Brand Item(s)
List Brand(s)
List by IDs
List Car by IDs
List Categories
List Contact
List Counter
List Event by IDs
List Fact Item(s)
List Featured Item
List Featured Properties
List Hotel by IDs
List Item
List Item(s)
List Item(s) Contact
List Item(s) Right
List Items
List Layout
List Location by ID
List Location by IDs
List Locations
List Plan(s)
List Properties
List Property
List Space by IDs
List Team(s)
List Terms
List Testimonial
List Text Item(s)
List Tour by IDs
List Trusted(s)
List Vendor
Listed By
Listing
Listing Title
Listing title
Listings
Live Editor
Ln
Load language from json success
Loaded :count strings
Loading...
Local
Locale
Location
Location Blocks
Location Button Link
Location Button Text
Location Categories
Location Category
Location Content
Location created
Location Desc
Location ID is not available
Location Information
Location Map
Location name
Location Name
Location not found
Location Search Style
Location updated
Locations
Log file >50M, please download it.
Log In
Log in
Log Out
Login
Login / Register
Login Register
Login with Facebook
Login with Google
Login with Twitter
Logo
Logo Color
Logo Dark
Logo Image
Logo Mobile
Logo Preload
Logo Text
Logo Transparent
Logout
Looking For
Lorem ipsum dolor sit amet, consectetur.
Lost your password?
m
Magic text generator
Mail
Mail Driver
Mail Encryption
Mail Host
Mail Password
Mail Port
Mail Username
Mailgun Domain
Mailgun Endpoint
Mailgun Secret
Mailing List
Main color
Main Currency
Main Menu
Main Settings
Make hide
Make publish
Manage Account
MANAGE ACCOUNT
Manage Agencies
Manage Agency
Manage Agent
Manage Boat
Manage Boats
Manage Bookings
Manage Car
Manage Cars
Manage category
Manage Contacts
Manage Coupon
Manage Course
Manage Courses
Manage Event
Manage Events
Manage Fields
Manage Flight
Manage Flights
Manage Hotel
Manage Hotels
Manage languages here
Manage languages of your website
MANAGE LISTINGS
Manage news
Manage News
Manage payouts
Manage Properties
Manage Property
Manage Rooms
Manage Rooms Availability
Manage Seats
Manage Space
Manage Spaces
Manage Terms
Manage Tour
Manage Tours
Map Background in Button Show Map
Map Clustering
Map Engine
Map fitBounds
Map Lat
Map Lat Default
Map Latitude
Map Layout
Map Left
Map Lng
Map Lng Default
Map Longitude
Map Options Default
Map Provider
Map Right
Map Search Fields
Map Search Layout
Map Zoom
Map Zoom Default
March
Margin
Mark all as read
Mark as cancelled
Mark as completed
Mark as verified
Mark as: :name
Maroon
Max
Max Adults
Max Area
Max Children
Max Guest
Max Guests
Max guests: 
Max passengers
Max People
Max Services
Maximum day for booking is 30
Maximum file size is: 
Maximum guests is :count
Maximum height allowed is: :number
Maximum per booking
Maximum Spend
Maximum upload file size is :max_size B
Maximum width allowed is: :number
May
Media
Media Management
Media Settings
Mega image url
Member does not exists
Member since
Member Since :time
Membership Plans
MENU
Menu
Menu Configs
Menu items
Menu Management
Menu name
Menu not found
Menus
Merchant Email
Message
Message host
Message to Administrator
Message to Customer
Messages
Messages :count
Method
Migration &amp; Seed Console Output:
Miles
Min
Min age
Min Age:
Min Area
Minimum advance reservations
Minimum amount to pay is :amount
Minimum day stay requirements
Minimum per booking
Minimum Spend
Minimum to pay
Minimum: :amount
Minute
Mo
Mobile
Mobile App Settings
Mobile Layout
Mobile: 
Model Name
Module Management
Module name
Modules
Modules for Booking Core
Monday
month
Month
Monthly
More
More Filter
More filters
More Info
More info
Most Popular :name
Move to Pending
Multi Attachments
Multi files attachment
Must be unique. Only accept letter and number, dash, underscore, without space
My Courses
My Current Plan
My Favorites
My plan
My Plan
My Plans
My plans
My profile
My Profile
My Properties
My Tickets
My Wallet
mysql
N/A
name
Name
Name (
                                     a -> z )
Name (
                                     z -> a )
Name ( a -> z )
Name ( z -> a )
Name (a -> z)
Name (z -> a)
Name [a->z]
Name [z->a]
Name *
Name is Required
Name of property
Name of the hotel
Name of vendor
Name on the Card
Name social
Name:
Name: :name
Nee Live Support?
Need Response
New
New Account
New booking has been made
New Credit Purchase Email Template
New password
New Password
New Password Again
New Password cannot be same as your current password. Please choose a different password.
New reply on ticket: #" . $this->ticket->id))->view('Support::email.new_reply
New request plan
New Tag
New Vendor Registration
Newest
News
News Categories
News Category
News content
News created
News does not exists
News Feed
News Management
News not found
News Settings
News Tag
News Tags
News updated
News: List Items
Nexmo Api Key
Nexmo Api Secret
Next
Next &raquo;
Next Post
Next Step
night
nights
Nights:
No
No Action is selected!
No Boat
No boat found
No boats found
No booking
No Booking
No Booking History
No Car
No car found
No cars found
No Coupon
No coupon found
No data
No data found
No Event
No event found
No events found
No file found
No Flight
No flight found
No Hotel
No hotel found
No Image
No information found
No item!
No Items
No items found
No items selected
No items selected!
No Maximum
No Minimum
No Module found
No of people
No payout methods available. Please contact administrator
No popup found
No properties found
No property found
No Review
No Room
No room available with your selected date. Please change your search critical
No room found
No rooms found
No Space
No space found
No spaces found
No ticket found
No tools available
No topic found
No Tours
No tours found
No verification field found
No Wishlist
No. Adults
No. Bathroom
No. Bed
No. Beds
No. Children
No. of Decimals
No. People
None
Normal
Normal 2
Normal Layout
Normal Ver 2
Normal White
Not Found
Not rated
Not Rated
Not signed up? 
Not sure? You can cancel this reservation up to 24 hours in advance for a full refund.
Not translated
not update status " . $response['event'])]);
                    }
                    else {
                        return response()->json(['status' => 'error
Not Verified
Not verified
Note
Note to admin
Note to admin: 
Note to vendor
Note to vendor:
Notes
Notes:
notice
Notice
Notifications
November
Number
Number Item
Number Item (Default: 4)
Number Items
Number of beds
Number of room
Number Star
Number star
Number Ticket
Number:
Object
October
of
Off
Offer Block
Office
Offline Payment
Oh no
Old
Old Password
Oldest
On
On ReCapcha
ON: Only post a review after booking - Off: Post review without booking
ON: Review must be approved by admin - OFF: Review is automatically approved
ON: Vendor can add service fee
ON: Vendor can change their booking paid amount
ON: Vendor can change their booking status
ON: When vendor posts a service, it needs to be approved by administrator
One Star
One-time
Only available on specific dates
Only Enquiry
Only Featured
Only featured items?
only for Services
Only For Services
Only For User
Only support json file
Oops! It looks like you're lost.
Opacity
Open
Open gallery when clicking Featured image on the Listing page?
Open Hours
Open modal settings
Open new tab
Open New Tab
OpenAI Settings
OpenStreetMap.org
OR
or
or continue with
or sign in with
Orange
Order
Order By
Order Date
Orientation
Origin
Origin: 
original
Original Text
Other
Other Block
Other Blocks
Other Settings
Our Agencies
Our Partners
Our Team
Overall rating number
Overall rating star
Overall rating text
OverLay
Overview
Owner
Padding
Page
Page 404 settings
Page Banner
Page become an expert
Page contact settings
Page Content
Page created
Page Detail
Page Expired
Page for Homepage
Page List
Page list properties layout
Page Management
Page navigation
Page not found
Page Search
Page single property layout
Page Sub Title
Page Title
Page updated
Pages
Pagination Navigation
pagination.next
pagination.previous
Paid
Paid:
Parent
Partial Payment
Passenger
Password
Password changed successfully !
Password Confirm
Password Confirm is required field
Password confirmation
Password is not correct
Password is required field
Password updated!
Password updated. Please re-login
Passwords must be at least eight characters and match the confirmation.
Pay Amount
Pay date
Pay date:
Pay deposit
Pay in full
Pay now
Payment
Payment Detail
Payment fail
Payment Failed
Payment Failed.
Payment gateway is not available
Payment gateway is required field
Payment gateway not found
Payment Gateways
Payment Information
Payment Method
Payment method
Payment not found
Payment Note
Payment Processing
Payment Required
Payment Settings
Payment updated
Payment Url
Payout history
Payout ID
Payout ID:
Payout information:
Payout Management
Payout Method
Payout method: 
Payout Methods
Payout Options
Payout request bulk action
Payout request has been created
Payout request management
Payouts
Payouts :count
Payouts Management
Paypal
PayPal does not support currency: :name
Payrexx Checkout
Paystack
Pending
People
People interest: :number
per 1 item
per adult
Per day
per Day: 
Per hour
per Hour: 
per month
per night
per person
per ticket
Per user [Default]
Percent
Percent (%)
period_end_date
period_interval
period_recurrences
period_start_date
Permalink
Permalink:
Permalink: 
Permanently delete
Permanently delete account
Permanently delete account will delete all services of that user and that user
Permanently delete success!
Permission Content
Permission Matrix
Permission Matrix updated
Permissions
Person Type
Person type
Person Types
Personal Information
pgsql
phone
Phone
Phone Click
Phone Contact
Phone is required field
Phone Number
Phone number must be E.164 format
Phone:
Phone: 
Photos must be JPEG or PNG format and least 2048x768
Pick to the Booking Status, that allows reviews after booking
Pick Up Date
Pickup
Pickup:
Pin
Pinterest
Plan
Plan Content
Plan created
Plan fail
Plan ID
Plan Name
Plan Report
Plan Request :count
Plan request management
Plan Request options
Plan saved
Plan updated
Please check the form below for errors
Please click the button below to verify your email address.
Please confirm access to your account by entering one of your emergency recovery codes.
Please confirm access to your account by entering the authentication code provided by your authenticator application.
Please enter envato username and license key (purchase code) to get autoupdate
Please enter field id and make sure it unique
Please enter field name
Please enter field type
Please enter license key
Please enter roles
Please input flag code
Please input language name
Please log in
Please make sure you back up data before updating
Please provide ticket content
Please select
Please select action
Please select an action!
Please select an Action!
Please select at lease one item
Please select at lease one room
Please select at least 1 item!
Please select at least one guest
Please select at least one number
Please select check-in and check-out date
Please select date!
Please select file
Please select how you want to configure the apps <code>.env</code> file.
Please select payment gateway
Please select Start and End date
Please select Start date
Please select start date
Please select start time!
Please select ticket!
Please verify captcha.
Please verify the captcha
Plus
PNG or JPG no bigger than 800px wide and tall.
Policy
Pool Size
Pool size
Poor
Popular Right Now
Popular Searches
Popular Topics
Popup
Popup created
Popup Management
Popup name
Popup updated
Popups
Position
Position Order
Post Comment
Post navigation
Postmark Token
Posts Per Page
PRESS ENTER TO SEE ALL SEARCH RESULTS
Prev
Preview
Preview Url
Preview Video Url
Previous
Previous Post
Price
Price - Number
Price (High to low)
Price (Low to high)
Price [high to low]
Price [low to high]
Price Excludes
Price Filter
Price filter
Price Includes
Price per day
Price per hour
Price per person
Price per ticket
Price Range
Price Ticket
Pricing
Pricing Icon
Pricing Packages
Primary
Primary button Role
Primary button Text
Print Ticket
Process now
Processing
Processing...
Production
Professional Information
Profile
Project ID
Properties
properties
Properties Availability
Properties Availability Calendar
property
Property
Property Attributes
Property by :name
Property Categories
Property Category
Property clone was successful
Property contact
Property Content
Property created
Property created by vendor must be approved by admin
Property Description
Property Details
Property Featured
Property featured
Property highlights
Property ID
Property Image
Property information
Property Management
Property Map by location
Property name
Property not found
Property not found!
Property Price
Property Sale Price
Property search
Property Settings
Property Showcase
Property Size
Property Status
Property type
Property Type
Property updated
Property views
Property: Form Search
Property: List Items
Property: Term Featured Box
Public key
Publish
Publishable Key
Purchase logs
Purchased user package successfully
Pusher
Pusher API
Pusher API Information
Pusher App Id
Pusher App Key
Pusher App Secret
Qa
QR Code scanned at: :time
Question
Queue Driver
Quizzes
Radius options
Rating
Rating (High to low)
Rating [high to low]
Ratings
Re-build language file for: :name success
Re-Password
Read
Read More
Read More Articles
Readonly
Ready to jump back in?
Real address
Real tour address
ReCaptcha Config
Received unknown event type
Recent Activities
Recent articles
Recent Bookings
Recent News
Recent updates
Recently Viewed
Recommended
Recovery
Recovery Boat Management
Recovery Boats
Recovery Car Management
Recovery Cars
Recovery Code
Recovery Event Management
Recovery Events
Recovery Flight Management
Recovery Flights
Recovery Hotel Management
Recovery Hotels
Recovery news
Recovery Popup Management
Recovery Space Management
Recovery Spaces
Recovery success!
Recovery Tour Management
Recovery Tours
Redis Driver
Redis Host
Redis Password
Redis Port
Redo
referenceId can't null
Regards
register
Register
Register Now
Register Options
Register success
Register success. Please wait for admin approval
Register successfully
Reject All
Reject all
Rejected
Related content
Related Hotel
Related Posts
Related Properties
Related topics
Remain
Remain can not smaller than 0
Remain:
Remember me
Remodal year
Remove
Remove section
Renew
Rent
Rental price
Rental price:
Rental's Location
Replies
Reply
Reply added
Reply Content
Reply content:
Reply created
Reports :count
Repurchase
Request created
Request exists
Request Information
Request join team
Request vendor success!
Required
Required fields are marked
Reserve a room
Reset
Reset all filters
Reset Password
Reset Password Notification
Resize
Resize & Continue
Resource ID is not available
Resource is not available
Resource not found
Restore boat success!
Restore car success!
Restore event success!
Restore flight success!
Restore hotel success!
Restore space success!
Restore tour success!
results
Return on another day
Return on same-day
Revenue
Review
review
Review Advanced Settings
Review content
Review Content
Review Content has at least 10 character
Review content has at least 10 character
Review Content is required field
Review criteria
Review error!
Review must be approval by admin
Review not enable
Review number per page
Review Options
Review rate
Review Score
Review success!
Review success! Please wait for admin approved!
Review title is required
Review Title is required field
reviews
Reviews
Reviews from guests
Right
Right (100$)
Right with space (100 $)
Role
Role Code
Role Content
Role created
Role Management
Role Manager
Role Name
Role request
Role updated
Roles
room
Room
Room Attributes
Room Availability
Room Availability Calendar
Room created
Room Description
Room Footage
Room information
Room Management
Room name
Room Name
room not found
Room not found
Room size
Room Size
Room Type
Room updated
Rooms
rooms
Rotate Left
Rotate Right
Rules
Sa
Sale Of Text
Sale off :number
Sale Price
Sandbox API Password
Sandbox API Username
Sandbox Signature
Saturation
Saturday
Save
SAVE :text
Save .env
Save and Install
Save As New Image
Save Block
Save Change
Save Changes
Save changes
Save for later
Save Menu
Save settings
Save Status
Save Template
Save up to 10%
Save up to 20%
Saving
Schedule
scripts after open of body tag
scripts before closing head tag
Scroll Down
Scroll Down ID
Scroll Now
Search
Search ...
Search Agency
Search Agent
Search Background Image
Search block ...
Search By
Search by code
Search by email
Search by key ...
Search by keyword
Search by name
Search by name or email
Search by name or ID
Search by name...
Search by payout id
Search by title
Search Category
Search Criteria
Search desc
Search engine
Search Engine
Search Field
Search file name....
Search for block...
Search for Boats
Search for Cars
Search for Courses
Search for Events
Search for Flights
Search for Properties
Search for Spaces
Search for Tours
Search for...
Search Form
Search Here
Search keyword ...
Search News
Search open tab
Search Options
Search Page
Search products…
Search Properties
Search result for: :name
Search results
Search results : ":s"
Search results: ":s"
Search Tag
Search topic
Search topic...
Search User
Search...
Seat Type
Seat type
Seat type Content
Seat Type Management
Seat type saved
Seat Type: :name
Seats
Secondary button Role
Secondary button Text
Secret access key
Secret Key
Secret key
Secret Word
Section
Section created
Section name
Section name is required
Section not found
Section updated
See All
See All :count Photos
See All :total Photos
See All Categories
See All Cities
See All Properties
See Availability
See details & photo
See less
See More
Select
Select an Action!
Select at leas 1 item!
Select at lease 1 item!
Select at least 1 item!
Select Attributes
Select booking status will be use for calculate payout of vendor
Select Categories
Select Category
Select Cloud Driver
Select Dates
Select default language
Select File
Select Files
Select from lists
Select images
Select Number
Select Payment Method
Select Properties
Select property
Select Room
Select Rooms
Select term car
Select term event
Select term property
Select term space
Select theme file
Select theme zip file:
Select Your Room
Send a message
Send a Messsage
Send email
Send Email Test
SEND MESSAGE
Send Message
Send now
Send Password Reset Link
Send request
Send Sms Test
Sends us a Message
SEO
Seo Description
SEO Options
Seo Title
September
Server Error
Server Requirements
Service
Service Account Key File Name
Service Boat
Service Car
Service Event
Service fee
Service Flight
Service Hotel
Service ID
Service ID is required
Service Info
Service is not bookable
Service name
Service not found
Service Space
Service Tour
Service Type
Service type is required
Service type not found
Service Unavailable
Service:
Services
Ses Key
Ses Region
Ses Secret
Session Driver
Set Paid
Setting
Setting options
Settings
Settings Email Enquiry
Settings Enquiry
Settings Enquiry for Service
Settings for 404 error page
Settings for block subscribe
Settings for contact page
Settings Saved
Setup accounts
Setup Application
Setup Database
Setup payout accounts
Setup Two Factor Authentication
Setup your payment accounts
Shapes
Share
Share Facebook
Share this post
Share Twitter
Short Description
Should be unique and letters only
Show agent email in profile?
Show agent phone in profile?
Show all
Show All
Show Attribute
Show every
Show Filter
Show less
Show More
Show more
Show on
Show on map
Show on the list
Show on the map
Show QR Code at the counter
Show Room Information
Show Type
Show vendor email in profile?
Show vendor phone in profile?
Showing
Showing :from - :to of :total
Showing :from - :to of :total boats
Showing :from - :to of :total Boats
Showing :from - :to of :total cars
Showing :from - :to of :total Cars
Showing :from - :to of :total coupon
Showing :from - :to of :total events
Showing :from - :to of :total Events
Showing :from - :to of :total Flights
Showing :from - :to of :total flights
Showing :from - :to of :total hotels
Showing :from - :to of :total Hotels
Showing :from - :to of :total posts
Showing :from - :to of :total properties
Showing :from - :to of :total rooms
Showing :from - :to of :total Rooms
Showing :from - :to of :total spaces
Showing :from - :to of :total Spaces
Showing :from - :to of :total tickets
Showing :from - :to of :total topics
Showing :from - :to of :total total
Showing :from - :to of :total Tour(s)
Showing :from - :to of :total tours
Showing :from - :to of :total Tours
Sidebar
Sidebar detail page
Sidebar Options
Sign in
Sign In
Sign In / Register
Sign in or create an account
Sign in or Register
Sign in to your account
Sign in with this account across the following sites.
Sign Up
Sign up
Sign up and we'll send the best deals to you
Sign up for free
Sign up for our mailing list to get latest updates and offers.
Signature
Similar Properties
single form search
Site Desc
Site Information
Site Name
Site title
Site url
Size
Size Column 1
Size Column 2
Size Column 3
Size Column 4
Size Column 6
Size Unit
Size:
Skill Level
Skill level
Slide 4 cards/slider
Slider Boatousel
Slider Carousel
Slider Carousel Simple
Slider Carousel V2
Slider Carousel Ver 2
Slider Ver 2
Slug
Sms Driver
SMS driver
SMS Event Booking
Sms Settings
Sms Testing
Social
Social Info
Social Information
Social Login
Sold
Sold Out
Some basic information or keywords
Someone
Sorry, but nothing matched your search terms. Please try again with some different keywords.
Sorry, no options found
Sorry, the current rooms are not enough for adults
Sorry, the current rooms are not enough for children
Sorry, we couldn't find the page you're looking for.
Sorry! No method available at the moment
Sort by
Sort by:
Space
Space Attributes
Space Blocks
Space by :name
Space by day
Space by night
Space clone was successful
Space Content
Space created
Space created by vendor must be approved by admin
Space Featured
Space information
Space Management
Space name
Space not found
Space not found!
Space Price
Space Sale Price
Space Settings
Space updated
Space Video
Space: Form Search
Space: List Items
Space: Term Featured Box
Spaces
Spaces Availability
Spaces Availability Calendar
Spam
Sparkpost Secret
Special Requirements
Specifications
Specifications Desc
Specifications List
Specifications name
Specs
Specs & Details
Speed
sq ft
Sq Ft
Sq Ft:
Sq:
Sqft
sqft
sqlite
sqlsrv
Square
square
Square feet
Square metre (m2)
Square Size Unit
Start Date
Start date
Start date is not a valid date
Start date:
Start Time
Start time booking
Start time booking: :time
Start Time:
Start Time: :time
Start Time: :time - 
Starting from
State
State/Province/Region
Status
Status is empty
Status:
Stay at least :number day
Stay at least :number days
Step 1 | Server Requirements
Step 2 | Permissions
Step 3 | Environment Settings
Step 3 | Environment Settings | Classic Editor
Step 3 | Environment Settings | Guided Wizard
Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.
Stories, tips, and guides
Stripe
Students
Style
Style 1
Style 1 : Background Color Only
Style 10
Style 2
Style 2 - Slider Carousel
Style 2 : Background Image
Style 2 : With Background Image
Style 3
Style 3 : Background Image + Color
Style 3 cards/ row
Style 4
Style 4 (Only List Item)
Style 5
Style 5 (Only List Item)
Style 5 cards/ row
Style 6
Style 7
Style 8
Style 9
Style Background
Style Normal
Style Settings
Su
Sub Currency
Sub Title
Sub title
Sub Title Page
Subject
Submit
Submit Review
Submitted On
Subscribe
Subscribe Image
Subscribe Style
Subscribe sub title
Subscribe title
Subscriber Info
Subscriber updated
Subscribers
Subtitle
Success
Successfully logged out
Sunday
Sunny Beach
Support
Support Category
Support Center
Support does not exists
Support Options
Support Settings
Support Tickets
Support tickets
Support updated
Supporter see all
Supporter View Type
Surroundings
System
System error
System Log Viewer
System Logs
System Updater
Tab button Boxed
Tab button Pills
Tab button Shadow
Tag
Tag Content
Tag Created
Tag name
Tag Slug
Tag updated
Tags
Tags:
Tags: 
Take off
Target
Teacher
Teacher config for course
Teacher Options
Teacher Setting
Team Member enable?
Team Members
Team members
Teams
Template
Template Builder
Template can\'t export. Please try again
Template Content
Template Management
Template Name
Template not found!
Term conditions is required field
Term Content
Term Icon
Term name
Term not found
Term not found!
Term saved
Term: :name
Terms & Conditions
Terms & Conditions page
terms and conditions
Terms and Conditions
Terrible
Test DB
Test Publishable Key
Test Secret Key
Testimonial Background (For Style 4, Style 6, Style 7)
Text
Text color
Text Featured Box
Text Image
Textarea
Th
Thank you for booking with us
Thank you for booking with us. Here are your booking information:
Thank you for contacting us! We will be in contact shortly.
Thank you for contacting us! We will get back to you soon
Thank you for subscribing
Thank you, we will contact you shortly
Thank You. Your booking was submitted successfully!
The :attribute must be at least :length characters and contain at least one number.
The :attribute must be at least :length characters and contain at least one special character and one number.
The :attribute must be at least :length characters and contain at least one special character.
The :attribute must be at least :length characters and contain at least one uppercase character and one number.
The :attribute must be at least :length characters and contain at least one uppercase character and one special character.
The :attribute must be at least :length characters and contain at least one uppercase character, one number, and one special character.
The :attribute must be at least :length characters and contain at least one uppercase character.
The :attribute must be at least :length characters.
The :name is required
The :name need to select at least :number days
The :name path is required
The booking status has been updated
The business name is required field
The City Maps
The email field is required
The first name is required field
The Following errors occurred:
The geographic coordinate
The last name is required field
The Maximum Spend does not include any Booking fee
The Minimum Spend does not include any Booking fee
The order has not reached the minimum value of :amount to apply the coupon code!
The page you're looking for isn't available. Try to search again or use the go to.
The password confirmation does not match
The password must be at least 6 characters
The position will be used to order in the Filter page search. The greater number is priority
The provided password does not match your current password.
The provided password was incorrect.
The provided two factor authentication code was invalid.
The provided two factor recovery code was invalid.
The resolution of the image is too big for the web. It can cause problems with Image Editor performance.
The terms and conditions field is required
The User name field is required.
Theme activated
Theme management
Theme Upload
Themes
There are :maxGuests guests available for your selected date
There are :numberGuestsCanBook guests available for your selected date
There are :numberTicket :titleTicket available for your selected date
There are many variations of passages.
There are two apples
There is 1 update.|There are :number updates.
There is no layer yet!
There is no room available at your selected dates
There was an error on row :row. :message
These credentials do not match our records.
These popular destinations have a lot to offer
This boat is not available at selected dates
This car is not available at selected dates
This coupon code has been used up!
This coupon code has expired!
This is a secure area of the application. Please confirm your password before continuing.
This Month
This order has exceeded the maximum value of :amount to apply coupon code! 
This password reset link will expire in :count minutes.
This password reset token is invalid.
This plan doesn't have annual pricing
This plan is not suitable for your role.
This property is not available at selected dates
This space is not available at selected dates
This theme does not have seeder class
This ticket does not belong to your events
This tour is not available at selected dates
This tour is not open on your selected day
This Week
This Year
Thoughtful thoughts to your inbox
Thousand Separator
Three Star
Thursday
ticket
Ticket
Ticket already scanned at :time
Ticket Assign To
Ticket created
Ticket ID
Ticket Management
Ticket name
Ticket not found
Ticket Options
Ticket scan success
Ticket Status
ticket_vip_1
Tickets
Tickets / Guests Information:
Time for check in
Time for check out
Time slot
Timeline
Timezone
Title
Title - Desc
Title - Link info
Title (using for slider ver 2)
Title for :service
Title for list
Title Link More
Title Page
Title Trusted
Title:
Title: About Us
Title: Day 1
Title/Desc
Title/Link
to
To
To (phone number)
To admin
To admin:
to book with your saved details or
To create payout request, please setup your payment account first
To customer
to discover more
to manage your bookings on the go!
To vendor:
To where
Today
Toggle
Toggle fullscreen
Too many login attempts. Please try again in :seconds seconds.
Too Many Requests
Tools
Top Experiences in :name
Top sights in  :text
Topbar Left Text
Topic
Topic created
Topic Management
Topic Tag
Topic Tags
Topics
Total
Total bookable services
Total Booking
Total bookings
Total Commission
Total Earning
Total earnings
Total Favorites
Total Fees
Total Free Customer Care
Total Pending
Total pending
Total Price
total results
Total Revenue
Total revenue
Total Room
Total Views
Total Visitor Reviews
Total:
Tour
Tour Attributes
Tour Blocks
Tour Booking Calendar
Tour Booking History
Tour by :name
Tour Categories
Tour Category
Tour clone was successful
Tour Content
Tour create by vendor must be approved by admin?
Tour created
Tour End Date
Tour Featured
Tour Filters
Tour Information
Tour information
Tour Location
Tour Locations
Tour Management
Tour Max People
Tour Min People
Tour name
Tour not found
Tour not found!
Tour Price
Tour Sale Price
Tour Settings
Tour snapshot
Tour Start Date
Tour Type
Tour updated
Tour Video
Tour: Box Category
Tour: Form Search
Tour: List Items
Tour: Tour Deals
Tour: Tour Types
Tours
Tours Availability
Tours Availability Calendar
Tracking
Tracking Report
Tracking Settings
Tracking System
Transaction not found
Translate
Translate for: :name
Translate Manager for: :name
Translated
Translated Text
Translation Manager
Translation manager of your website
Translation saved
Translations
Transparent
Transparent V2
Transparent V3
Transparent V4
Transparent V5
Transparent V6
Transparent V7
Transparent V8
Transparent V9
Trash
travellers
Trip Ideas
True
Try changing your filter criteria
Tu
Tuesday
Turn on the mode for booking form
Turn on the mode for contact form
Turn on the mode for login form
Turn on the mode for register form
Turn on the mode for reviewing agency
Turn on the mode for reviewing agent
Turn on the mode for reviewing boat
Turn on the mode for reviewing car
Turn on the mode for reviewing course
Turn on the mode for reviewing event
Turn on the mode for reviewing flight
Turn on the mode for reviewing hotel
Turn on the mode for reviewing news
Turn on the mode for reviewing property
Turn on the mode for reviewing space
Turn on the mode for reviewing tour
Tw
Twilio Account Sid
Twilio Account Token
Twitter
Twitter Client Id
Twitter Client Secret
Twitter Description
Twitter Image
Twitter Title
Two Checkout
Two Factor Authentication
Two factor authentication is now enabled. Scan the following QR code using your phone's authenticator application.
Two Star
Two-factor authentication adds an additional layer of security to your account by requiring more than just a password to sign in
Type
Type does not exists
Type is required
Typography
Unable to save the .env file, Please create it manually.
Unassigned
Unauthorized
Undo
Unique Code
Unlimited
Unlimited Usage
Unmissable Destinations
Unpaid
Unread
unselect
Update booking
Update booking core
Update fail!
Update now
Update Profile
Update request plan
Update Success
Update success
Update success!
Update Successful
Update successfully
Update verification data
Update Verification Data
Updated
Updated success!
Updated successfully!
Updater
Updater Booking Core
Upgrade
Upgrade for :price
Upgrade Request :count
Upgrade to PRO to unlock unlimited access to all of our features
Upload
Upload image
Upload image size 30px
Upload Now
Upload Profile Files
Upload Theme
URL
Url
Url callback: 
Url is required
Usage Limit per Coupon
Usage Limit Per User
Usage Limits
Usage Restriction
Use a recovery code
Use an authentication code
Use file
Use for
Use Form Wizard
Use this content
Used/Total
User
User blocked
User created
User Info
User input
User Name
User name
User not found
User Notes
User Plan Management
User Plans
User Plans :count
User Plans Options
User Plans Settings
User Register Default Role
User Settings
User Social
User updated
User upgrade request
User: List Users
Users
Users :count
Users on your team
validation.phone
Value
Vendor
Vendor Auto Approved?
Vendor Commission Type
Vendor commission value
Vendor config for boat
Vendor config for car
Vendor config for event
Vendor config for flight
Vendor config for hotel
Vendor config for space
Vendor config for tour
Vendor contact
Vendor dashboard
Vendor Dashboard
Vendor Enable?
Vendor information
Vendor News
Vendor Options
Vendor Payouts
Vendor Plan Meta
Vendor plan updated
Vendor Plans
Vendor Profile
Vendor Register
Vendor Register Form
Vendor Registration Approved
Vendor Requests
Vendor Role
Vendor Settings
Vendor Teams
Vendor User
Vendor: 
Verification
Verification data
Verification data saved. Please wait for admin approval
Verification Request
Verification Request :count
Verification Requests
Verifications
Verified
Verify
Verify code do not match
Verify Configs
Verify email
Verify Email Address
Verify email cancel!
Verify email successfully!
Verify Phone
Verify request: :email
Verify Your Email Address
Version
Version 2
Version 3
Very Good
Video
Video Background
Video Block
Video Caption
Video Link
Video Player
Video Url
Video URL
Video Url (Youtube, Vimeo, ..)
View
View :name
View agency
View all
View All
View all (:total)
View All Destinations
View All Option
View all reviews (:total)
View All Url
View All With 3 Items Grid
View Boat
View Car
View City
View Course
View dashboard
View detail
View Detail
View Details
View Event
View file
View Hotel
View in a map
View Less
View Listings
View More
View My Listing
View On Map
View on map
View page
View payouts
View Photos
View Post
View Profile
View Property
View request
View Space
View Ticket
View ticket
View Tour
View Verification
View verification data
Views
views
Views and manage system log of your website
W
Wallet
Wallet Settings
warning
Warning
Watch Video
Watermark
We
We are glad to see you again!
We couldn't find any boats.
We couldn't find any cars.
We couldn't find any events.
We couldn't find any hotels.
We couldn't find any properties.
We couldn't find any spaces.
We couldn't find any tours.
We only work with the best companies around the globe
We use cookies!
Webhook error while validating signature.
Webhook Secret
Webhook url: <code>:code</code>
Website
Website Click
Websites
Wednesday
week
Week
Welcome
Welcome :name!
Welcome back
Welcome to
Welcome to :name
Welcome to the update wizard.
Welcome To The Updater
What are you looking for?
What to know before visiting :text
What's Nearby
When two factor authentication feature is enabled, the user is required to input a six digit numeric token during the authentication process. This token is generated using a time-based one-time password (TOTP) that can be retrieved from any TOTP compatible mobile authentication application such as Google Authenticator.
When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application.
Where are you going?
Where you’ll be
Which attribute show in listing page?
Whoops!
Why Book With Us?
Why Choose us
Why Choose Us
width
Wifi available
Wifi Available
Wildcard allowed. Eg: */checkout/* 
WishList
Wishlist
With selected:
Would you like to reduce resolution before editing the image?
Wrapper Class (opt)
Write a 
Write a Review
Write a review
Write Your Comment
Write your own custom css code
x
y
year
Year
Year built
Year Built
Yes
Yes please
Yes, please
Yes, please disable it
Yes, please enable it
Yes,please enable it
Yes! Successfully connected to the DB: ".DB::connection()->getDatabaseName()));
            }else{
                return $this->sendSuccess(false , __("Could not find the database. Please check your configuration.
Yesterday
You are already subscribed
You are not a student of this course
You are not allowed to delete this folder
You are not allowed to edit this folder
You are not allowed to register
You are receiving this email because we approved your vendor registration request.
You are receiving this email because we received a password reset request for your account.
You are receiving this email because we updated your vendor verification data.
You are using latest version of Booking Core
You are using newest version of Booking Core: :version
You booking has been changed successfully
You can approved the request here:
You can book room with full day
You can check all payout request here:
You can check the ticket here:
You can check your dashboard here:
You can check your information here:
You can check your payout request here:
You can edit on main lang.
You can enable and disable your payment gateways here
You can not add yourself
You can not edit menu in demo mode
You cancelled the payment
You cannot book your own service
You cannot review your service
You cant save cookie
You do not have permission to access
You does not select payout method or you need to enter account info for that method
You don not have enough :amount for payout
You don't have access.
You don't have any notifications
You don't have permission delete the file!
You got new reply for ticket: #
You got reply from :name
You got reply from vendor. 
You have enabled factor authentication
You have just done the become agent request, please wait for the Admin\'s approved
You have just done the become vendor request, please wait for the Admin's approved
You have just gotten a new Subscriber
You have not enabled factor authentication
You have to login in to do this
You have to verify email first
You haven't selected return day or hours
You May Like
You might also like
You might also like...
You must <a href='#login' data-bs-toggle='modal' data-target='#login'>log in</a> to write review
You must <a href='#login' data-toggle='modal' data-target='#login'>log in</a> to write review
You must book the service for :number days in advance
You must enable on main lang.
You must to book a minimum of :number days
You must to book a minimum of :number nights
You need return boat on same-day
You need to create the template at the Main-language tab first!
You need to log in to use the coupon code!
You need to make a booking or the Orders must be confirmed before writing a review
You need to return the boat on the same-day
You payment has been processed successfully
You payment has been processed successfully before
You will get all notifications from this email
Your .env file settings have been saved.
Your account
Your account has been blocked
Your account info
Your account information has been saved
Your account information was verified
Your account will be permanently deleted. Once you delete your account, there is no going back. Please be certain.
Your avatar
Your balance is zero
Your Booking
Your booking status has been updated
Your booking status is: :status
your booking was submitted successfully!
Your Card Information
Your City
Your credit balance is :amount
Your current password does not matches with the password you provided. Please try again.
Your current version: :version
Your Email
Your email address will not be published.
Your has created a plan request
Your Information
Your license key (Purchase code)
Your license key: :key
Your menu has been saved
Your Message
Your Messages
Your Name
Your note:
Your password has been reset!
Your payment has been canceled
Your payment has been placed
Your payout request has been deleted
Your payout request has been rejected
Your payout request has been rejected:
Your payout request has been submitted
Your payout request has been submitted:
Your payout request has been updated
Your payout request has been updated:
Your Phone
Your plan request has been approved
Your plan request has been cancelled
Your Rating & Review
Your selected dates are not valid
Your selected room is not available. Please search again
Your service has new booking
Your template has been saved
Your Travel Journey Starts Here
Your trip
Your upgrade request has approved already
Youtube Image
Youtube Link
Youtube link
Youtube link video
Youtube Video
YYYY/MM/DD
Zip Code
ZIP code/Postal code
