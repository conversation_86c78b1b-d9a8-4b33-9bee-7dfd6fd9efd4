{"The provided password does not match your current password.": "Le mot de passe fourni ne correspond pas à votre mot de passe actuel.", "-- Select --": "-- S<PERSON><PERSON><PERSON>ner --", ":num mins": ":num mins", ":num min": ":num min", ":num hours": ":num heures", ":num hour": ":num heure", ":num days": ":num jours", ":num day": ":num jour", ":num weeks": ":num semaines", ":num week": ":num semaine", ":num months": ":num mois", ":num month": ":num mois", ":num years": ":num années", ":num year": ":num année", "sqft": "pi²", "Draft": "Brouillon", "Unpaid": "Non payé", "Paid": "<PERSON><PERSON>", "Processing": "En cours de traitement", "Completed": "<PERSON><PERSON><PERSON><PERSON>", "Confirmed": "<PERSON><PERSON><PERSON><PERSON>", "Cancelled": "<PERSON><PERSON><PERSON>", "Cancel": "Annuler", "Pending": "En attente", "Partial Payment": "Paiement partiel", "Failed": "<PERSON><PERSON><PERSON>", "Phone": "Téléphone", "Number": "<PERSON><PERSON><PERSON><PERSON>", "Email": "E-mail", "Attachment": "Pièce jointe", "Multi Attachments": "Pièces jointes multiples", "Text": "Texte", "D": "J", "H": "H", ":count Days": ":count Jours", ":count Day": ":count Jo<PERSON>", ":count Hours": ":count <PERSON><PERSON>", ":count Hour": ":count <PERSON><PERSON>", "Show on the map": "Afficher sur la carte", "Login": "Connexion", "Can not authorize": "Impossible d'autoriser", "Email :email exists. Can not register new account with your social email": "L'e-mail :email existe. Impossible de créer un nouveau compte avec votre e-mail social", "User blocked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Your account has been blocked": "Votre compte a été bloqué", "Sign Up": "S'inscrire", "News": "Actualités", "Access denied for user!. Please check your configuration.": "Accès refusé pour l'utilisateur ! Veuillez vérifier votre configuration.", "Yes! Successfully connected to the DB: \".DB::connection()->getDatabaseName()));\r\n            }else{\r\n                return $this->sendSuccess(false , __(\"Could not find the database. Please check your configuration.": "Oui ! Connexion réussie à la base de données : \".DB::connection()->getDatabaseName()));\r\n            }else{\r\n                return $this->sendSuccess(false , __(\"Impossible de trouver la base de données. Veuillez vérifier votre configuration.", "For security, please change your password to continue": "Pour des raisons de sécurité, veuillez changer votre mot de passe pour continuer", "Upgrade for :price": "Mettre à niveau pour :price", "Please verify captcha.": "Veuillez vérifier le captcha.", "Publish": "Publier", "Blocked": "<PERSON><PERSON><PERSON><PERSON>", "Manage Agencies": "<PERSON><PERSON><PERSON> les agences", "All": "<PERSON>ut", "Item not found": "Élément non trouvé", "Create Agency": "<PERSON><PERSON><PERSON> une agence", "Create a Agency": "<PERSON><PERSON><PERSON> une agence", "Agent only belong one agencies": "L'agent n'appartient qu'à une seule agence", "Agency updated": "Agence mise à jour", "Agency created": "Agence créée", "No items selected!": "Aucun élément sélectionné !", "Please select an action!": "Veuillez sélectionner une action !", "Delete success!": "Suppression réussie !", "Update success!": "Mise à jour réussie !", "System error": "<PERSON><PERSON>ur système", "Title": "Titre", "Sub Title": "Sous-titre", "List Item(s)": "Liste d'éléments", "Name": "Nom", "Type": "Type", "Avatar Image": "Image d'avatar", "Our Team": "Notre équipe", "Image": "Image", "Our Partners": "Nos partenaires", "Agent Register Form": "Formulaire d'inscription d'agent", "Our Agencies": "Nos agences", "Manage Agency": "<PERSON><PERSON><PERSON> l'agence", "Agency not found!": "Agence non trouvée !", "Edit Agency :name": "Modifier l'agence :name", "Edit Agency": "Modifier l'agence", "Agency fail": "Échec de l'agence", "Manage Agent": "<PERSON><PERSON>rer l'agent", "Agency :name": "Agence :name", "All Agent": "Tous les agents", "Email is required field": "L'e-mail est un champ obligatoire", "Email invalidate": "E-mail invalide", "Password is required field": "Le mot de passe est un champ obligatoire", "The first name is required field": "Le prénom est un champ obligatoire", "The last name is required field": "Le nom de famille est un champ obligatoire", "The business name is required field": "Le nom de l'entreprise est un champ obligatoire", "Can not register": "Impossible de s'inscrire", "Register success": "Inscription réussie", "Register success. Please wait for admin approval": "Inscription réussie. Veuillez attendre l'approbation de l'administrateur", "Success": "Su<PERSON>ès", "Find Agents": "Trouver des agents", "Thank you for contacting us! We will get back to you soon": "Merci de nous avoir contactés ! Nous vous répondrons bientôt", "[:site_name] New message": "[:site_name] Nouveau message", "Agencies": "Agences", " Publish ": " Publier ", " Move to Draft ": " <PERSON><PERSON><PERSON><PERSON> vers brouillon ", " Delete ": " <PERSON><PERSON><PERSON><PERSON> ", "agent": "agent", "All Agency": "Toutes les agences", "Add Agency": "Ajouter une agence", "Agencies Settings": "Paramètres des agences", "Agent Settings": "Paramètres de l'agent", "Edit: ": "Modifier : ", "Add new agency": "Ajouter une nouvelle agence", "Permalink": "Lien permanent", "View agency": "Voir l'agence", "Save Changes": "Enregistrer les modifications", "Author Setting": "Paramètres de l'auteur", "-- Select User --": "-- Sélectionner un utilisateur --", "Feature Image": "Image en vedette", "Agency Content": "Contenu de l'agence", "Agency name": "Nom de l'agence", "Content": "Contenu", "Office": "Bureau", "Mobile": "Mobile", "Fax": "Fax", "Banner Image": "Image de bannière", "List Agent": "Liste des agents", "User": "Utilisa<PERSON>ur", "-- Select Agent --": "-- <PERSON><PERSON><PERSON><PERSON><PERSON> un agent --", "Add item": "Ajouter un élément", "Locations": "Emplacements", "Location": "Emplacement", "-- Please Select --": "-- <PERSON><PERSON><PERSON><PERSON> --", "Real address": "<PERSON><PERSON><PERSON>", "The geographic coordinate": "La coordonnée géographique", "Search by name...": "Rechercher par nom...", "Map Latitude": "Latitude de la carte", "Map Longitude": "Longitude de la carte", "Map Zoom": "Zoom de la carte", "Social Info": "Informations sociales", "Name social": "Nom social", "Code icon": "Code d'icône", "Link social": "Lien social", "Agencies list": "Liste des agences", " Bulk Actions ": " Actions en lot ", "Do you want to delete?": "Voulez-vous supprimer ?", "Apply": "Appliquer", "Search by name": "Rechercher par nom", "Search": "<PERSON><PERSON><PERSON>", "Found :total items": ":total éléments trouvés", "Author": "<PERSON><PERSON><PERSON>", "Status": "Statut", "Reviews": "<PERSON><PERSON>", "Date": "Date", "[Author Deleted]": "[Auteur supprimé]", "Edit": "Modifier", "No data": "<PERSON><PERSON><PERSON> don<PERSON>", "Page Search": "Recherche de page", "Config page search of your website": "Configurer la page de recherche de votre site web", "General Options": "Options générales", "Title Page": "Page de titre", "Review Options": "Options d'avis", "Config review for agency": "Configurer les avis pour l'agence", "Enable review system for Agency?": "Activer le système d'avis pour l'agence ?", "Yes, please enable it": "<PERSON><PERSON>, veuillez l'activer", "Turn on the mode for reviewing agency": "Activer le mode d'évaluation de l'agence", "Review must be approval by admin": "L'avis doit être approuvé par l'administrateur", "Yes please": "<PERSON>ui s'il vous plaît", "ON: Review must be approved by admin - OFF: Review is automatically approved": "ON : L'avis doit être approuvé par l'administrateur - OFF : L'avis est automatiquement approuvé", "Review number per page": "Nombre d'avis par page", "Break comments into pages": "Diviser les commentaires en pages", "Agent Register": "Inscription d'agent", "Agent Auto Approved?": "Agent auto-approuvé ?", "Agent Role": "Rôle de l'agent", "You can edit on main lang.": "Vous pouvez modifier dans la langue principale.", "Agent Profile": "Profil de l'agent", "Show agent email in profile?": "Afficher l'e-mail de l'agent dans le profil ?", "Show agent phone in profile?": "Afficher le téléphone de l'agent dans le profil ?", "Content Email Agent Registered": "Contenu de l'e-mail d'inscription de l'agent", "Content email send to Agent or Administrator when user registered.": "Contenu de l'e-mail envoy<PERSON> à l'agent ou à l'administrateur lors de l'inscription de l'utilisateur.", "Enable send email to customer when customer registered ?": "Activer l'envoi d'e-mail au client lors de son inscription ?", "You must enable on main lang.": "<PERSON><PERSON> de<PERSON> activer dans la langue principale.", "Email to agent subject": "Objet de l'e-mail à l'agent", "Email to agent content": "Contenu de l'e-mail à l'agent", "Enable send email to Administrator when customer registered ?": "Activer l'envoi d'e-mail à l'administrateur lors de l'inscription du client ?", "Email to Administrator subject": "Objet de l'e-mail à l'administrateur", "Email to Administrator content": "Contenu de l'e-mail à l'administrateur", "Config review for agent": "Configurer les avis pour l'agent", "Enable review system for Agent?": "Activer le système d'avis pour l'agent ?", "Turn on the mode for reviewing agent": "Activer le mode d'évaluation de l'agent", "Hello Administrator": "Bon<PERSON><PERSON> Administrateur", "Here are new contact information:": "Voici les nouvelles informations de contact :", "Message": "Message", "Password is not correct": "Le mot de passe n'est pas correct", "You are not allowed to register": "Vous n'êtes pas autorisé à vous inscrire", "The terms and conditions field is required": "Le champ conditions générales est obligatoire", "Register successfully": "Inscription réussie", "The email field is required": "Le champ e-mail est obligatoire", "Update successfully": "Mise à jour réussie", "Successfully logged out": "Déconnexion réussie", "Current password is not correct": "Le mot de passe actuel n'est pas correct", "Password updated. Please re-login": "Mot de passe mis à jour. Veuillez vous reconnecter", "Booking not found!": "Réservation non trouvée !", "You do not have permission to access": "Vous n'avez pas l'autorisation d'accéder", "Booking Details": "Détails de la réservation", "Location ID is not available": "L'ID de l'emplacement n'est pas disponible", "Location not found": "Emplacement non trouvé", "News not found": "Actualité non trouvée", "You have to login in to do this": "Vous devez vous connecter pour faire cela", "Type is required": "Le type est obligatoire", "Type does not exists": "Le type n'existe pas", "Resource is not available": "La ressource n'est pas disponible", "Resource ID is not available": "L'ID de la ressource n'est pas disponible", "Resource not found": "Ressource non trouvée", "Boat ID is not available": "L'ID du bateau n'est pas disponible", "Mobile App Settings": "Paramètres de l'application mobile", "Mobile Layout": "Mise en page mobile", "Choose Layout for Mobile app": "Choisir la mise en page pour l'application mobile", "Boat": "<PERSON><PERSON>", "Attributes": "Attributs", "Attributes not found!": "Attributs non trouvés !", "Attribute: :name": "Attribut : :name", "Attribute saved": "Attribut enregistré", "Select at least 1 item!": "Sélectionnez au moins 1 élément !", "Select an Action!": "Sélectionnez une action !", "Updated success!": "Mise à jour réussie !", "Term not found": "Terme non trouvé", "Term: :name": "Terme : :name", "Term saved": "<PERSON><PERSON><PERSON> enregistré", "Boats": "Bateaux", "Boat Management": "Gestion des bateaux", "Recovery": "Récupération", "Recovery Boat Management": "Gestion de récupération des bateaux", "Add Boat": "Ajouter un bateau", "Add new Boat": "Ajouter un nouveau bateau", "Edit Boat": "Modifier le bateau", "Edit: :name": "Modifier : :name", "DEMO MODE: can not add data": "MODE DÉMO : impossible d'ajouter des données", "Boat updated": "Bateau mis à jour", "Boat created": "Bateau c<PERSON>", "Deleted success!": "Suppression réussie !", "Permanently delete success!": "Suppression définitive réussie !", "Recovery success!": "Récupération réussie !", "Clone success!": "Clonage réussi !", "Style Background": "Arrière-plan de style", "Normal": "Normal", "Slider Boatousel": "Carrousel de bateaux", "- Layout Normal: Background Image Uploader": "- Mise en page normale : Téléchargeur d'image d'arrière-plan", "- Layout Slider: List Item(s)": "- Mise en page carrousel : Liste d'éléments", "Background Image Uploader": "Téléchargeur d'image d'arrière-plan", "Service Boat": "Service bateau", "Boat: Form Search": "Bateau : Formulaire de recherche", "Desc": "Description", "Number Item": "Numéro d'élément", "Style": "Style", "Slider Carousel": "Carrousel", "Filter by Location": "Filtrer par emplacement", "Order": "Commande", "Date Create": "Date de création", "Order By": "Trier par", "ASC": "ASC", "DESC": "DESC", "Only featured items?": "Seulement les éléments en vedette ?", "List by IDs": "Liste par ID", "Boat: List Items": "Bateau : Liste d'éléments", "Normal Layout": "Mise en page normale", "Map Layout": "Mise en page carte", "Availability": "Disponibilité", "Boats Availability": "Disponibilité des bateaux", "Boat not found": "Bateau non trouvé", "per Hour: ": "par heure : ", "per Day: ": "par jour : ", "Hour: ": "Heure : ", "Day: ": "Jour : ", "Full Book": "Réservation complète", "You need to return the boat on the same-day": "<PERSON><PERSON> retourner le bateau le même jour", "This boat is not available at selected dates": "Ce bateau n'est pas disponible aux dates sélectionnées", "Update Success": "Mise à jour réussie", ":count boats found": ":count bateaux trouvés", ":count boat found": ":count bateau trouvé", "Showing :from - :to of :total Boats": "Affichage de :from - :to sur :total bateaux", "Manage Boats": "<PERSON><PERSON><PERSON> bateaux", "Recovery Boats": "Récupérer les bateaux", "Restore boat success!": "Restauration du bateau réussie !", "Create": "<PERSON><PERSON><PERSON>", "Create Boats": "<PERSON><PERSON>er des bateaux", "Boat not found!": "Bateau non trouvé !", "Edit Boats": "Modifier les bateaux", "Delete boat success!": "Suppression du bateau réussie !", "No item!": "Aucun élément !", "Not Found": "Non trouvé", "Update success": "Mise à jour réussie", "Update fail!": "Échec de la mise à jour !", "Search for Boats": "Rechercher des bateaux", "Can not check availability": "Impossible de vérifier la disponibilité", "Your selected dates are not valid": "<PERSON><PERSON> dates sélectionnées ne sont pas valides", "You must book the service for :number days in advance": "V<PERSON> devez réserver le service :number jours à l'avance", "You haven't selected return day or hours": "Vous n'avez pas sélectionné le jour ou les heures de retour", "Start time booking: :time": "Heure de début de réservation : :time", "End time booking: :time": "Heure de fin de réservation : :time", "You need return boat on same-day": "<PERSON><PERSON> retourner le bateau le même jour", "Please select": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ner", "day": "jour", "hour": "heure", "guest": "invité", "Not rated": "Non évalué", ":number Boats": ":number Bateaux", ":number Boat": ":number <PERSON><PERSON>", "Filter Price": "Filtrer le prix", "Review Score": "Score d'avis", "All Boats": "To<PERSON> les bateaux", "Manage Boat": "<PERSON><PERSON><PERSON> le bateau", "Boat Settings": "Paramètres du bateau", "Add new attribute": "Ajouter un nouvel attribut", "Attribute Content": "Contenu de l'attribut", "Save Change": "Enregistrer les modifications", "Attribute name": "Nom de l'attribut", "Position Order": "Ordre de position", "Ex: 1": "Ex : 1", "The position will be used to order in the Filter page search. The greater number is priority": "La position sera utilisée pour ordonner dans la page de recherche de filtre. Le nombre le plus élevé est prioritaire", "Display Type in detail service": "Type d'affichage dans le service détaillé", "Display Left Icon": "Afficher l'icône de gauche", "Display Center Icon": "Afficher l'icône du centre", "Hide in detail service": "Masquer dans le service détaillé", "Enable hide": "<PERSON><PERSON> le masqua<PERSON>", "Hide in filter search": "Masquer dans la recherche de filtre", "Boat Attributes": "Attributs du bateau", "Add Attributes": "Ajouter des attributs", "Add new": "Ajouter nouveau", " Bulk Action ": " Action en lot ", "All Attributes": "Tous les attributs", "Actions": "Actions", "Manage Terms": "<PERSON><PERSON><PERSON> les termes", "Boats Availability Calendar": "Calendrier de disponibilité des bateaux", "Showing :from - :to of :total boats": "Affichage de :from - :to sur :total bateaux", "No boats found": "Aucun bateau trouvé", "Date Information": "Informations sur la date", "Date Ranges": "Plages de dates", "Available for booking?": "Disponible pour la réservation ?", "Price per hour": "Prix par heure", "Price per day": "Prix par jour", "Close": "<PERSON><PERSON><PERSON>", "Save changes": "Enregistrer les modifications", "Today": "<PERSON><PERSON><PERSON>'hui", "Boat Content": "Contenu du bateau", "Youtube Video": "Vidéo Youtube", "Youtube link video": "Lien vid<PERSON>o Youtube", "FAQs": "FAQ", "Eg: When and where does the tour end?": "Ex : Quand et où se termine le circuit ?", "Eg: Can I bring my pet?": "Ex : <PERSON><PERSON><PERSON>-je amener mon animal de compagnie ?", "Gallery": "Galerie", "Extra Info": "Informations supplémentaires", "Guest": "Invi<PERSON>", "Example: 3": "", "Cabin": "", "Example: 5": "", "Length": "", "Example: 30m": "", "Speed": "", "Example: 25km/h": "", "Specs": "", "Eg: Range": "", "Eg: 6000km": "", "Cancellation Policy": "", "Full refund up to 4 days prior.": "", "Additional Terms & Information": "", "For Sanitary purposes ONLY, although there is a working toilet and shower, we've deactivated the shower and the toliet is for limited use (urine only..pardon the graphic detail!)...": "", "Include": "", "Eg: Specialized bilingual guide": "", "Exclude": "", "Eg: Additional Services": "", "Loading...": "", "Pricing": "", "Minimum advance reservations": "", "Ex: 3": "", "Leave blank if you dont need to use the min day option": "", "Start time booking": "Heure de début de réservation", "End time booking": "Heure de fin de réservation", "*Leave it blank if don't use these fields. The end-time must be larger than start-time": "*Laissez vide si vous n'utilisez pas ces champs. L'heure de fin doit être supérieure à l'heure de début", "Enable extra price": "Activer le prix supplémentaire", "Extra Price": "Prix supplémentaire", "Price": "Prix", "Extra price name": "Nom du prix supplémentaire", "One-time": "Une fois", "Service fee": "Frais de service", "Enable service fee": "Activer les frais de service", "Buyer Fees": "<PERSON><PERSON> d'ache<PERSON>ur", "Fee name": "Nom des frais", "Fee desc": "Description des frais", "Fixed": "Fixe", "Percent": "Pourcentage", "Price per person": "Prix par personne", "Add new boat": "Ajouter un nouveau bateau", "View Boat": "Voir le bateau", "Boat Featured": "Bateau en vedette", "Enable featured": "<PERSON>r la mise en vedette", "Is Instant Booking?": "Réservation instantanée ?", "Enable instant booking": "Activer la réservation instantanée", "Default State": "État par défaut", "-- Please select --": "-- <PERSON><PERSON><PERSON><PERSON> --", "Always available": "Toujours disponible", "Only available on specific dates": "Disponible uniquement à des dates spécifiques", " Recovery ": " Récupération ", "Permanently delete": "Supprimer définitivement", "Move to Pending": "<PERSON>éplacer vers en attente", " Clone ": " <PERSON><PERSON><PERSON> ", "Advanced": "<PERSON><PERSON><PERSON>", "Featured": "En vedette", "No boat found": "Aucun bateau trouvé", "Banner Page": "<PERSON> de bannière", "Layout Search": "Recherche de mise en page", "Location Search Style": "Style de recherche d'emplacement", "Autocomplete from locations": "Autocomplétion à partir des emplacements", "Autocomplete from Gmap Place": "Autocomplétion à partir de Google Maps", "Limit item per Page": "Limiter les éléments par page", "Default: 9": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> : 9", "Radius options": "Options de rayon", "Miles": "<PERSON>", "Km": "Km", "Layout Map Option": "Option de mise en page de carte", "Map Left": "Carte à gauche", "Map Right": "Carte à droite", "Map Lat Default": "Latitude de carte par défaut", "Map Lng Default": "Longitude de carte par défaut", "Map Zoom Default": "Zoom de carte par défaut", "Get lat - lng in here": "Obt<PERSON>r lat - lng ici", "Icon Marker in Map": "Marqueur d'icône sur la carte", "SEO Options": "Options SEO", "Share Facebook": "Partager sur Facebook", "Share Twitter": "Partager sur Twitter", "Seo Title": "Titre SEO", "Enter title...": "Entrez le titre...", "Seo Description": "Description SEO", "Enter description...": "Entrez la description...", "Featured Image": "Image en vedette", "Facebook Title": "Titre Facebook", "Facebook Description": "Description Facebook", "Facebook Image": "Image Facebook", "Twitter Title": "Titre Twitter", "Twitter Description": "Description Twitter", "Twitter Image": "Image Twitter", "Config review for boat": "Configurer les avis pour le bateau", "Enable review system for Boat?": "Activer le système d'avis pour le bateau ?", "Turn on the mode for reviewing boat": "Activer le mode d'évaluation du bateau", "Customer must book a boat before writing a review?": "Le client doit réserver un bateau avant d'écrire un avis ?", "ON: Only post a review after booking - Off: Post review without booking": "ON : Publier un avis uniquement après réservation - Off : Publier un avis sans réservation", "Allow review after making Completed Booking?": "Autoriser les avis après avoir effectué une réservation terminée ?", "Pick to the Booking Status, that allows reviews after booking": "Choisir le statut de réservation qui permet les avis après réservation", "Leave blank if you allow writing the review with all booking status": "Laissez vide si vous autorisez l'écriture d'avis avec tous les statuts de réservation", "Review criteria": "Critères d'évaluation", "Eg: Service": "Ex : Service", "Booking Buyer Fees Options": "Options de frais d'acheteur de réservation", "Config buyer fees for boat": "Configurer les frais d'acheteur pour le bateau", "Vendor Options": "Options du vendeur", "Vendor config for boat": "Configuration du vendeur pour le bateau", "Boat created by vendor must be approved by admin": "Le bateau créé par le vendeur doit être approuvé par l'administrateur", "ON: When vendor posts a service, it needs to be approved by administrator": "ON : Quand le vendeur publie un service, il doit être approuvé par l'administrateur", "Allow vendor can change their booking status": "Autoriser le vendeur à changer le statut de sa réservation", "ON: Vendor can change their booking status": "ON : Le vendeur peut changer le statut de sa réservation", "Allow vendor can change their booking paid amount": "Autoriser le vendeur à changer le montant payé de sa réservation", "ON: Vendor can change their booking paid amount": "ON : Le vendeur peut changer le montant payé de sa réservation", "Allow vendor can add service fee": "Autoriser le vendeur à ajouter des frais de service", "ON: Vendor can add service fee": "ON : Le vendeur peut ajouter des frais de service", "Booking Deposit": "Dépôt de réservation", "Booking Deposit Options": "Options de dépôt de réservation", "Deposit Amount": "<PERSON><PERSON>", "Deposit Fomular": "Formule de dépôt", "Default": "<PERSON><PERSON> <PERSON><PERSON>", "Deposit amount + Buyer free": "<PERSON><PERSON> du dépôt + <PERSON><PERSON><PERSON> gratuit", "Disable boat module?": "<PERSON><PERSON><PERSON>r le module bateau ?", "Disable boat module": "Désactiver le module bateau", "Yes, please disable it": "<PERSON><PERSON>, ve<PERSON><PERSON><PERSON> le d<PERSON>r", "Form Search Fields": "", "Search Criteria": "", "Search Field": "", "Service name": "", "Attribute": "", "-- Select field type --": "", "-- Select Attribute --": "", "Size Column 6": "", "Size Column 4": "", "Size Column 3": "", "Size Column 2": "", "Size Column 1": "", "Map Search Fields": "", "Advance": "", "Add new term": "", "Term Content": "", "Term name": "", "Class Icon": "", "get icon in <a href=':link_1' target='_blank'>fontawesome.com</a> or <a href=':link_2' target='_blank'>icofont.com</a>": "", "Ex: fa fa-facebook": "", "Upload image size 30px": "", "All the Term's image are same size": "", "Add Term": "", "All Terms": "", "Boat information": "", "Booking Number": "Numéro de réservation", "Booking Status": "Statut de réservation", "Payment method": "Méthode de paiement", "Payment Note": "Note de paiement", "Boat name": "Nom du bateau", "Address": "<PERSON><PERSON><PERSON>", "Start date": "Date de début", "End date:": "Date de fin :", "Durations:": "Durées :", "Rental price:": "Prix de location :", "Extra Prices:": "Prix supplémentaires :", "Coupon": "Coupon", "Total": "Total", "Remain": "Reste", "Manage Bookings": "<PERSON><PERSON><PERSON> les réservations", "Checkout": "Commande", "You have to verify email first": "V<PERSON> devez d'abord vérifier votre e-mail", "Service not found": "Service non trouvé", "Please verify the captcha": "Veuillez vérifier le captcha", "The password confirmation does not match": "La confirmation du mot de passe ne correspond pas", "The password must be at least 6 characters": "Le mot de passe doit contenir au moins 6 caractères", "Your credit balance is :amount": "Votre solde de crédit est :amount", "Term conditions is required field": "Les conditions générales sont un champ obligatoire", "Payment gateway is required field": "La passerelle de paiement est un champ obligatoire", "Payment gateway not found": "Passerelle de paiement non trouvée", "Payment gateway is not available": "La passerelle de paiement n'est pas disponible", "You payment has been processed successfully": "Votre paiement a été traité avec succès", "Service type not found": "Type de service non trouvé", "Service is not bookable": "Le service n'est pas réservable", "You cannot book your own service": "Vous ne pouvez pas réserver votre propre service", "Thank you for contacting us! We will be in contact shortly.": "Merci de nous avoir contactés ! Nous vous contacterons bientôt.", "Remain can not smaller than 0": "Le reste ne peut pas être inférieur à 0", "Booking not found": "Réservation non trouvée", "You don't have access.": "Vous n'avez pas accès.", "You booking has been changed successfully": "Votre réservation a été modifiée avec succès", "You got reply from :name": "Vous avez reçu une réponse de :name", "[:site_name] New inquiry has been made": "[:site_name] Une nouvelle demande a été faite", "[:site_name] You get new inquiry request": "[:site_name] V<PERSON> avez reçu une nouvelle demande de renseignements", "[:site_name] New booking has been made": "[:site_name] Une nouvelle réservation a été faite", "[:site_name] Your service got new booking": "[:site_name] Votre service a reçu une nouvelle réservation", "Thank you for booking with us": "Merci d'avoir réservé avec nous", "[:site_name] The booking status has been updated": "[:site_name] Le statut de réservation a été mis à jour", "Your booking status has been updated": "Le statut de votre réservation a été mis à jour", "Your payment has been canceled": "Votre paiement a été annulé", "Thank you, we will contact you shortly": "<PERSON><PERSON><PERSON>, nous vous contact<PERSON><PERSON> bi<PERSON>", "Enable Offline Payment?": "", "Custom Name": "", "Offline Payment": "", "Custom Logo": "", "Custom HTML Description": "", "Enable Paypal Standard?": "", "Paypal": "", "Enable Sandbox Mod?": "", "Convert To": "", "In case of main currency does not support by PayPal. You must select currency and input exchange_rate to currency that PayPal support": "", "Exchange Rate": "", "Example: Main currency is VND (which does not support by PayPal), you may want to convert it to USD when customer checkout, so the exchange rate must be 23400 (1 USD ~ 23400 VND)": "", "Sandbox API Username": "", "Sandbox API Password": "", "Sandbox Signature": "", "API Username": "", "API Password": "", "Signature": "", "Booking status does need to be paid": "", "Booking total is zero. Can not process payment gateway!": "", "Payment Failed": "", "You cancelled the payment": "", "PayPal does not support currency: :name": "", "Exchange rate to :name must be specific. Please contact site owner": "", "Enable Payrexx Checkout?": "", "Payrexx Checkout": "", "Instance name": "", "Api secret key": "", "Url callback: ": "", "Your payment has been placed": "", "Payment Processing": "", "Payment Failed.": "", "You payment has been processed successfully before": "", "No information found": "", "referenceId can't null": "", "Enable Paystack gateway?": "", "Paystack": "", "Public key": "", "Secret key": "", "Payment Url": "", "Merchant Email": "", "not update status \" . $response['event'])]);\r\n                    }\r\n                    else {\r\n                        return response()->json(['status' => 'error": "", "Enable Stripe Checkout V2?": "", "Stripe": "", "Secret Key": "", "Publishable Key": "", "Enable Sandbox Mode": "", "Test Secret Key": "", "Test Publishable Key": "", "Webhook Secret": "", "Webhook url: <code>:code</code>": "", "Webhook error while validating signature.": "", "Payment not found": "", "Received unknown event type": "", "Buy credits": "", ":name has created new Booking": "", ":name has changed to :status": "", ":name has sent a Enquiry for :title": "", ":name has updated the PAID amount on :title": "", "Administrator has updated the PAID amount on :title": "", "Revenue": "", "Total revenue": "", "Earning": "", "Total Earning": "", "Bookings": "", "Total bookings": "", "Services": "", "Total bookable services": "", "Total Revenue": "", "Total pending": "", "Earnings": "", "Total earnings": "", "Total Pending": "", "Total Fees": "", "Total Commission": "", "Total Booking": "", "Payment fail": "", "Transaction not found": "", "Payment updated": "", "Plan fail": "", "Plan updated": "", "Booking Settings": "", "Payment Settings": "", "Enquiry Settings": "", "Guest Checkout": "", "Enable guest checkout": "", "Yes, please": "", "Enable Ticket/Guest information": "", "Checkout Page": "", "Change your checkout page options": "", "Enable reCapcha Booking Form": "", "On ReCapcha": "", "Turn on the mode for booking form": "", "Terms & Conditions page": "", "Invoice Page": "", "Change your invoice page options": "", "Invoice Logo": "", "Invoice Company Info": "", "Settings Enquiry for Service": "", "Change your enquiry options": "", "Enable enquiry for Hotel": "", "Enable enquiry form": "", "Enquiry Type": "", "Booking & Enquiry": "", "Only Enquiry": "", "Enable enquiry for Tour": "", "Enable enquiry for Space": "", "Enable enquiry for Car": "", "Enable enquiry for Event": "", "Enable enquiry for Boat": "", "Settings Enquiry": "", "Enable re-catpcha for enquiry?": "", "Enable re-captcha at enquiry form": "", "Settings Email Enquiry": "", "Change your email enquiry options": "", "Enable send email to Vendor": "", "Email to Vendor content": "", "Enable send email to Administrator": "", "Currency": "", "Currency Format": "", "Main Currency": "", "Format": "", "Right (100$)": "", "Right with space (100 $)": "", "Left ($100)": "", "Left with space ($ 100)": "", "Thousand Separator": "", "Decimal Separator": "", "No. of Decimals": "", "Extra Currency": "", "Sub Currency": "", "Exchange rate": "", "Example: Main currency is VND, and the extra currency is USD, so the exchange rate must be 23400 (1 USD ~ 23400 VND)": "", "Payment Gateways": "", "You can enable and disable your payment gateways here": "", "Your Information": "", "First name": "", "Last name": "", "Address line 1": "", "Address line 2": "", "City": "", "State/Province/Region": "", "ZIP code/Postal code": "", "Country": "", "Special Requirements": "", "Credit want to pay?": "", "Credit": "", "Pay now": "", "How do you want to pay?": "", "Pay deposit": "", "Pay in full": "", "Create a new account?": "", "First Name": "", "Last Name": "", "<EMAIL>": "", "Your Phone": "", "Password": "", "Password confirmation": "", "Your City": "", "I have read and accept the": "", "terms and conditions": "", "Submit": "", "Select Payment Method": "", "Booking Submission": "", "your booking was submitted successfully!": "", "Booking details has been sent to:": "", "Booking Date": "", "Payment Method": "", "Booking History": "", "Name on the Card": "", "Card Name": "", "Card Number": "", "Expiration": "", "CVC": "", "Enquiry": "", "Name *": "", "Email *": "", "Note": "", "Send now": "", "Car": "", "Cars": "", "Car Management": "", "Recovery Car Management": "", "Add Car": "", "Add new Car": "", "Edit Car": "", "Car updated": "", "Car created": "", "Select term car": "", "Service Car": "", "Car: Term Featured Box": "", "Car: Form Search": "", "Car: List Items": "", "Cars Availability": "", "Car not found": "", ":count cars found": "", ":count car found": "", "Showing :from - :to of :total Cars": "", "Manage Cars": "", "Recovery Cars": "", "Restore car success!": "", "Create Cars": "", "Car not found!": "", "Edit Cars": "", "Delete car success!": "", "Search for Cars": "", "This car is not available at selected dates": "", "You must to book a minimum of :number days": "", "Please select date!": "", ":number Cars": "", ":number Car": "", "All Cars": "", "Manage Car": "", "Car Settings": "", "Car Attributes": "", "Cars Availability Calendar": "", "Showing :from - :to of :total cars": "", "No cars found": "", "Instant Booking?": "", "Car Content": "", "Passenger": "", "Gear Shift": "", "Example: Auto": "", "Baggage": "", "Door": "", "Example: 4": "", "Ical": "", "Import url": "", "Export url": "", "Car Number": "", "Car Price": "", "Sale Price": "", "Car Sale Price": "", "If the regular price is less than the discount , it will show the regular price": "", "Minimum day stay requirements": "", "Ex: 2": "", "Leave blank if you dont need to set minimum day stay option": "", "Per day": "", "Add new car": "", "View Car": "", "Car Featured": "", "No car found": "", "Config review for car": "", "Enable review system for Car?": "", "Turn on the mode for reviewing car": "", "Customer must book a car before writing a review?": "", "Config buyer fees for car": "", "Vendor config for car": "", "Car created by vendor must be approved by admin": "", "Disable car module?": "", "Disable car module": "", "Car information": "", "Car name": "", "Days:": "", "Adults": "", "Children": "", ":count day": "", ":count days": "", "Contact Submissions": "", "Please select at least 1 item!": "", "No Action is selected!": "", "Class Block": "", "Other Block": "", "Contact Block": "", "Contact Page": "", "Contact": "", "All Contact Submissions": "", "Search...": "", "SEND MESSAGE": "", "Primary": "", "Footer": "", "Menus": "", "Create new menu": "", "Page": "", "News Category": "", "Menu not found": "", "You can not edit menu in demo mode": "", "Your menu has been saved": "", "Module Management": "", "All Notifications": "", "DEMO MODE: Disable setting update": "", "Settings Saved": "", "Please enter license key": "", "Can not connect to update server. Please check again": "", "You are using latest version of Booking Core": "", "Can not get update file from server": "", "Can not download update file to folder storage": "", "Can not un-zip the package": "", "License information has been saved": "", "You cant save cookie": "", "Clear cache success!": "", "Dashboard": "", "Setting": "", "installer_messages.environment.wizard.form.name_required": "", "installer_messages.environment.wizard.form.db_connection_failed": "", ":name has created :services :title": "", ":name has created a Review :review on :title": "", ":title has been deleted by :by": "", ":title was updated to :status by :by": "", "Custom": "", "General Settings": "", "Menu": "", "Tools": "", "Modules": "", "Languages": "", "Translation Manager": "", "System Logs": "", "System": "", "Advanced Settings": "", "Style Settings": "", "Vendor": "", "-- Vendor --": "", "-- All Location --": "", "-- All --": "", "Only Featured": "", "Edit Menu:": "", "Menu name": "", "No items found": "", "Add to Menu": "", "Custom Url": "", "URL": "", "Link Text": "", "Menu items": "", "Label": "", "Class": "", "Target": "", "Open new tab": "", "Enable mega menu": "", "Columns": "", "2 columns": "", "3 columns": "", "4 columns": "", "Mega image url": "", "Delete": "", "Origin: ": "", "Menu Configs": "", "Save Menu": "", "Menu Management": "", "All Menus": "", "Use for": "", "All Modules": "", "Active": "", "Deactivate": "", "Module name": "", "Description": "", "Version": "", "No Module found": "", "Unread": "", "Read": "", "You don't have any notifications": "", "Search engine": "", "Search Engine": "", "Allow search engines to show this service in search results?": "", "Yes": "", "No": "", "Leave blank to use service title": "", "Search Options": "", "Search open tab": "", "Current Tab": "", "Open New Tab": "", "Square Size Unit": "", "Size Unit": "", "Square metre (m2)": "", "Square feet": "", "Map Provider": "", "Change map provider of your website": "", "OpenStreetMap.org": "", "Google Map": "", "Gmap API Key": "", "Learn how to get an api key": "", "Map Options Default": "", "Map Clustering": "", "Off": "", "On": "", "Map fitBounds": "", "Social Login": "", "Change social login information for your website": "", "Facebook": "", "Enable Facebook Login?": "", "Facebook Client Id": "", "Facebook Client Secret": "", "Google": "", "Enable Google Login?": "", "Google Client Id": "", "Google Client Secret": "", "Twitter": "", "Enable Twitter Login?": "", "Twitter Client Id": "", "Twitter Client Secret": "", "Captcha": "", "ReCaptcha Config": "", "Enable ReCaptcha": "", "Version 2": "", "Version 3": "", "Api Key": "", "Api Secret": "", "Custom Scripts for all languages": "", "Add custom HTML script before and after the content, like tracking code": "", "Custom Scripts": "", "Head Script": "", "scripts before closing head tag": "", "Body Script": "", "scripts after open of body tag": "", "Footer Script": "", "Custom Scripts for :name": "", "Cookie agreement": "", "Cookie agreement config": "", "-- Select one --": "", "Style 1": "", "Style 2": "", "Agree Text Button": "", "Site Information": "", "Information of your website for customer and goole": "", "Site title": "", "Site Desc": "", "Date format": "", "Timezone": "", "-- Default --": "", "Change the first day of week for the calendars": "", "Monday": "", "Sunday": "", "Language": "", "Change language of your websites": "", "Select default language": "", "Manage languages here": "", "Enable Multi Languages": "", "Enable": "", "Enable RTL": "", "Homepage": "", "Change your homepage content": "", "Page for Homepage": "", "Header & Footer Settings": "", "Change your options": "", "Logo": "", "Favicon": "", "Topbar Left Text": "", "Footer List Widget": "", "Size": "", "Footer Text Left": "", "Footer Text Right": "", "Page contact settings": "", "Settings for contact page": "", "Contact title": "", "Contact sub title": "", "Contact Desc": "", "Contact Featured Image": "", "Cookie preferences": "", "Cookie Settings Modal": "", "Cookie Title": "", "Primary button Text": "", "Accept all": "", "Primary button Role": "", "Accept selected": "", "Secondary button Text": "", "Settings": "", "Secondary button Role": "", "Open modal settings": "", "Accept necessary": "", "Button save setting text": "", "Save settings": "", "Button Accept All text": "", "Accept All": "", "Button Reject All text": "", "Reject All": "", "Setting options": "", "Action": "", "Toggle": "", "Readonly": "", "Value": "", "Config Broadcast": "", "Change your config broadcast site": "", "Broadcast Driver": "", "Pusher API": "", "Change your API for pusher here. It will use for chat plugin and notification": "", "Pusher API Information": "", "API KEY": "", "API Secret": "", "APP ID": "", "Cluster": "", "General Style": "", "Change main color, typo ...": "", "Main color": "", "Typography": "", "Font Family": "", "Color": "", "Font Size": "", "Line Height": "", "Font Weight": "", "bold or 400": "", "H1,H2,H3 Options": "", "H1 Font Family": "", "H2 Font Family": "", "H3 Font Family": "", "Custom CSS for all languages": "", "Write your own custom css code": "", "Custom CSS": "", "Custom CSS for :name": "", "Config Sms": "", "SMS driver": "", "Sms Driver": "", "Config Nexmo Driver": "", "Nexmo Api Key": "", "Nexmo Api Secret": "", "From": "", "Config Twilio Driver": "", "Twilio Account Sid": "", "Twilio Account Token": "", "SMS Event Booking": "", "Phone number must be E.164 format": "", "[+][country code][subscriber number including area code]": "", "Example": "", "Config Phone Administrator": "", "Admin Phone": "", "Create Booking": "", "Administrator": "", "Customer": "", "Enable send sms to Administrator when have booking?": "", "Message to Administrator": "", "Enable send sms to Vendor when have booking?": "", "Message to Customer": "", "Enable send sms to Customer when have booking?": "", "Update booking": "", "Enable send sms to Administrator when update booking?": "", "Enable send sms to Vendor when update booking?": "", "Enable send sms to Customer when update booking?": "", "Sms Testing": "", "To (phone number)": "", "Send Sms Test": "", "Main Settings": "", "Modules for Booking Core": "", "Manage languages of your website": "", "Translations": "", "Translation manager of your website": "", "System Log Viewer": "", "Views and manage system log of your website": "", "Updater": "", "Updater Booking Core": "", "Clear Cache": "", "Clear Cache for Booking Core": "", "No tools available": "", "System Updater": "", "Update booking core": "", "You are using newest version of Booking Core: :version": "", "Your license key: :key": "", "Last check for update: :date": "", "Last update success: :date": "", "Check for update": "", "Your current version: :version": "", "Latest version available: :version": "", "I already back up all files and database": "", "Update now": "", "or": "", "change license info": "", "License Key Information": "", "Please enter envato username and license key (purchase code) to get autoupdate": "", "Envato username": "", "Your license key (Purchase code)": "", "How can I get my license key?": "", "Warning": "", "Please make sure you back up data before updating": "", "Confirmation": "", "Are you want to update now?. Please make sure you backup all your files and database first": "", "Notice": "", "Coupon Management": "", "All Coupons": "", "Edit Coupon: :name": "", "Create Coupon": "", "Coupon updated": "", "Coupon created": "", "Invalid coupon code!": "", "Coupon code is applied successfully!": "", "Coupon code is added already!": "", "This coupon code has expired!": "", "The order has not reached the minimum value of :amount to apply the coupon code!": "", "This order has exceeded the maximum value of :amount to apply coupon code! ": "", "Coupon code is not applied to this product!": "", "You need to log in to use the coupon code!": "", "Coupon code is not applied to your account!": "", "This coupon code has been used up!": "", "Add new Coupon": "", "General": "", "Coupon Code": "", "Unique Code": "", "Coupon Name": "", "Coupon Amount": "", "Discount Type": "", "Amount": "", "End Date": "", "2021-12-12 00:00:00": "", "Usage Restriction": "", "Minimum Spend": "", "No Minimum": "", "The Minimum Spend does not include any Booking fee": "", "Maximum Spend": "", "No Maximum": "", "The Maximum Spend does not include any Booking fee": "", "Only For Services": "", "-- Select Services --": "", "Only For User": "", "Usage Limits": "", "Usage Limit per Coupon": "", "Unlimited Usage": "", "Usage Limit Per User": "", "Add new coupon": "", "Code": "", "Is Vendor": "", "No coupon found": "", "Coupon code": "", "[Remove]": "", "Course": "", "Category": "", "Category :name": "", "Category saved": "", "Courses": "", "Course Management": "", "Add Course": "", "Add new Course": "", "Edit Course": "", "Course updated": "", "Course created": "", "Lectures Management": "", "Course not found": "", "Lecture not found": "", "Lecture updated": "", "Lecture created": "", "Delete lecture successfully!": "", "Skill Level": "", "Level": "", "Level :name": "", "Level saved": "", "Section not found": "", "Section updated": "", "Section created": "", "Delete section successfully!": "", "Style 3": "", "Filter by Category": "", "Courses: List Items": "", "You are not a student of this course": "", "Manage Courses": "", "Create Courses": "", "Course not found!": "", "Edit Courses": "", "Manage Course": "", "Booking Report": "", "Add video lecture": "", "Add scorm lecture": "", "Add presentation lecture": "", "Add iframe lecture": "", "Lecture name is required": "", "Section name is required": "", "File is required": "", "Url is required": "", "Duration is required": "", "Search for Courses": "", "\":title\" has been added to your cart.": "", ":duration Hours": "", ":duration Minutes": "", "Course Category": "", "Course Level": "", "All Courses": "", "Add new course": "", "Categories": "", "Levels": "", "Course Settings": "", "Course Attributes": "", "Courses Availability Calendar": "", "Add new category": "", "View": "", "Category Content": "", "Category name": "", "Parent": "", "Add Category": "", "Slug": "", "Course Content": "", "Short Description": "", "Duration": "", "Ex: 100": "", "Hours": "", "If left blank, the total time of the lectures will automatically be calculated": "", "Preview Video Url": "", "Video Url": "", "Map Engine": "", "Map Lat": "", "Map Lng": "", "View Course": "", "Course Options": "", "Course Featured": "", "Teacher Setting": "", "All Users": "", "Add new user": "", "Search User": "", "Role": "", "Change Password": "", "All Course": "", "Teacher": "", "Students": "", "[Teacher Deleted]": "", "No data found": "", "Add Section": "", "Add lecture": "", "Add video": "", "Add presentation": "", "Add Iframe": "", "Add SCORM": "", "Edit section": "", "Remove section": "", "Edit Lecture": "", "Lecture name": "", "File": "", "File URL": "", "Duration (minute)": "", "in minutes": "", "Preview Url": "", "Inactive": "", "Display Order": "", "Edit Section": "", "Section name": "", "Add new level": "", "Level Content": "", "Level name": "", "Add level": "", "Sub Title Page": "", "Config review for course": "", "Enable review system for Course?": "", "Turn on the mode for reviewing course": "", "Customer must book a course before writing a review?": "", "Config buyer fees for course": "", "Teacher Options": "", "Teacher config for course": "", "Job created by vendor must be approved by admin": "", "Disable course module?": "", "Disable course module": "", "Course name": "", "Ratings": "", "Instructors": "", "Class icon featured": "", "Welcome :name!": "", "Earning statistics": "", "Recent Bookings": "", "More": "", "Item": "", "Created At": "", "[Deleted]": "", "Timeline": "", "Currency: :currency_main": "", "Yesterday": "", "Last 7 Days": "", "Last 30 Days": "", "This Month": "", "Last Month": "", "This Year": "", "This Week": "", "DEMO MODE: Disable update": "", "Email Settings": "", "Config Email": "", "Change your config email site": "", "Email Driver": "", "Email Host": "", "Email Port": "", "Email Encryption": "", "Email Username": "", "Email Password": "", "Mailgun Domain": "", "Mailgun Secret": "", "Mailgun Endpoint": "", "Postmark Token": "", "Ses Key": "", "Ses Secret": "", "Ses Region": "", "Sparkpost Secret": "", "Email From Config": "", "How your customer can contact to you": "", "Admin Email": "", "You will get all notifications from this email": "", "Email Form Name": "", "Email Form Address": "", "Email Testing": "", "Send Email Test": "", "Email Header & Footer": "", "Change booking email header and footer": "", "Header": "", "Event": "", "Events": "", "Event Management": "", "Recovery Event Management": "", "Add Event": "", "Add new Event": "", "Edit Event": "", "Event updated": "", "Event created": "", "Service Event": "", "Event: Form Search": "", "Event: List Items": "", "Events Availability": "", "Event not found": "", ":count events found": "", ":count event found": "", "Showing :from - :to of :total Events": "", "Manage Events": "", "Recovery Events": "", "Restore event success!": "", "Create Events": "", "Event not found!": "", "Edit Events": "", "Delete event success!": "", "Search for Events": "", "Start date is not a valid date": "", "Please select ticket!": "", "There are :numberTicket :titleTicket available for your selected date": "", "Please select start time!": "", ":slot not available for your selected ": "", "ticket": "", ":number Events": "", ":number Event": "", "All Events": "", "Manage Event": "", "Event Settings": "", "Event Attributes": "", "Events Availability Calendar": "", "Showing :from - :to of :total events": "", "No events found": "", "Add new event": "", "View Event": "", "Event Featured": "", "Event Content": "", "Start Time": "", "Ex: 15:00": "", "Input time format, ex: 15:00": "", "End Time": "", "Ex: 21:00": "", "Input time format, ex: 21:00": "", "Duration (hour)": "", "Duration Unit": "", "Hour": "", "Minute": "", "Event Price": "", "Event Sale Price": "", "Tickets": "", "Price - Number": "", "ticket_vip_1": "", "Price Ticket": "", "Number Ticket": "", "Per hour": "", "Price per ticket": "", "No event found": "", "Config review for event": "", "Enable review system for Event?": "", "Turn on the mode for reviewing event": "", "Customer must book a event before writing a review?": "", "Booking Options": "", "Config Booking for event": "", "Booking Type": "", "Ticket": "", "Time slot": "", "Booking Buyer Fees": "", "Vendor config for event": "", "Event created by vendor must be approved by admin": "", "Disable event module?": "", "Disable event module": "", "Event information": "", "Event name": "", "Duration:": "", ":count hour": "", ":count hours": "", "Start Time:": "", "Airline": "", "Airline Management": "", "Edit airline": "", "Airline saved": "", "Airport": "", "Airport Management": "", "Edit airport": "", "Airport saved": "", "Import Queued": "", "Flight": "", "Flights": "", "Flight Management": "", "Recovery Flight Management": "", "Add Flight": "", "Add new Flight": "", "Edit Flight": "", "Edit: #:name": "", "Flight updated": "", "Flight created": "", "Flight: :name :code #:id": "", "Flight seat": "", "Flight seat Management": "", "Edit flight_seat": "", "Flight seat saved": "", "Seat Type": "", "Seat Type Management": "", "Seat type": "", "Edit seat type": "", "Seat type saved": "", "Flight: Form Search": "", "Flight Blocks": "", ":count flights found": "", ":count flight found": "", "Showing :from - :to of :total Flights": "", "Manage Flights": "", "Recovery Flights": "", "Restore flight success!": "", "Create Flights": "", "Flight not found!": "", "Edit Flights": "", "Delete flight success!": "", "Flight clone was successful": "", "Flight: :name": "", "All Flight seats": "", "Create Flight seat": "", "Edit  :name": "", "Flight seat updated": "", "Flight seat created": "", "Delete room success!": "", "Search for Flights": "", ":number Flights": "", ":number Flight": "", "All Flights": "", "Manage Flight": "", "Add Flights": "", "Flight Settings": "", "Airline Content": "", "Airline: :name": "", "Add Airline": "", "Airport Content": "", "IATA Code": "", "Airport: :name": "", "Import from IATA": "", "Add Airport": "", " Mark as Publish ": "", " Mark as Draft ": "", "Found :count airport(s)": "", "Flight Attributes": "", "Add new flight": "", " Flight Ticket type": "", "Add new seat type": "", "Flight Content": "", "-- Select Airport from --": "", "To": "", "-- Select Airport to --": "", "Airline and time": "", "-- Select Airline --": "", "Departure time": "", "Arrival time": "", "hours": "", "Search by code": "", "All Flight": "", "-- Select seat type --": "", "Max passengers": "", "Person type": "", "Adult": "", "Child": "", "Baggage Check in": "", "Baggage Cabin": "", "Add Flight Seat": "", "All Flight seat": "", "Airport From": "", "Airport To": "", "Flight ticket": "", "No flight found": "", "Seat type Content": "", "Seat Type: :name": "", "Add Seat Type": "", "From where": "", "To where": "", "Config review for flight": "", "Enable review system for Flight?": "", "Turn on the mode for reviewing flight": "", "Customer must book a flight before writing a review?": "", "Config Booking for flight": "", "Flight by day": "", "Flight by night": "", "Vendor config for flight": "", "Flight created by vendor must be approved by admin": "", "Disable flight module?": "", "Disable flight module": "", "Flight information": "", "Flight name": "", "Nights:": "", "Rental price": "", "Hotel": "", "Hotels": "", "Hotel Management": "", "Add Hotel": "", "Add new Hotel": "", "Recovery Hotel Management": "", "Edit Hotel": "", "Hotel updated": "", "Hotel created": "", "Room Attributes": "", "Hotel: :name": "", "Room Management": "", "All Rooms": "", "Edit room: :name": "", "Room updated": "", "Room created": "", "Hotel: Form Search": "", "Service Hotel": "", "Hotel: List Items": "", "Room Availability": "", "Hotel not found": "", "room not found": "", "Room not found": "", ":count hotels found": "", ":count hotel found": "", "Showing :from - :to of :total Hotels": "", "Dates are not valid": "", "Maximum day for booking is 30": "", "Manage Hotels": "", "Recovery Hotels": "", "Create Hotels": "", "Space not found!": "", "Edit Hotels": "", "Delete hotel success!": "", "Restore hotel success!": "", "Create Room": "", "Search for Spaces": "", "Please select at lease one room": "", "There is no room available at your selected dates": "", "Your selected room is not available. Please search again": "", "The :name need to select at least :number days": "", "Sorry, the current rooms are not enough for adults": "", "Sorry, the current rooms are not enough for children": "", "Please select check-in and check-out date": "", "rooms": "", "room": "", ":number Hotels": "", ":number Hotel": "", ":count nights": "", ":count night": "", "Hotel Star": "", "Rooms": "", "Hotel Room": "", "All Hotels": "", "Manage Hotel": "", "Hotel Settings": "", "Hotel Attributes": "", "Add new hotel": "", "Manage Rooms": "", "View Hotel": "", "Hotel Featured": "", "Hotel Content": "", "Name of the hotel": "", "Hotel Policy": "", "Hotel rating standard": "", "Eg: 5": "", "Policy": "", "Eg: What kind of foowear is most suitable ?": "", "Check in/out time": "", "Allowed full day booking": "", "Enable full day booking": "", "You can book room with full day": "", "Eg: booking from 22-23, then all days 22 and 23 are full, other people cannot book": "", "Time for check in": "", "Eg: 12:00AM": "", "Time for check out": "", "Eg: 11:00AM": "", "Hotel Price": "", "Hotel Sale Price": "", "Surroundings": "", "Distance": "", "Sunny Beach": "", "m": "", "km": "", "Edit hotel": "", "Manage Rooms Availability": "", "No hotel found": "", "Room Availability Calendar": "", "Showing :from - :to of :total rooms": "", "No rooms found": "", "Number of room": "", "Add new Hotel Room": "", "Room information": "", "Room Name": "", "Room name": "", "Room Description": "", "Number of beds": "", "Room Size": "", "Room size": "", "Max Adults": "", "Max Children": "", "Back to hotel": "", "Add Room": "", "No room found": "", "Guests": "", "Layout Item Hotel In Page Search": "", "List Item": "", "Grid Item": "", "Which attribute show in listing page?": "", "Config review for hotel": "", "Enable review system for Hotel?": "", "Turn on the mode for reviewing hotel": "", "Customer must book a hotel before writing a review?": "", "Config buyer fees for hotel": "", "Vendor config for hotel": "", "Hotel created by vendor must be approved by admin": "", "Disable hotel module?": "", "Disable hotel module": "", "Hotel information": "", "Hotel name": "", "Check in": "", "Check out:": "", "Language created": "", "Language Management": "", "Language updated": "", "Translate for: :name": "", "Translation saved": "", "Folder: resources/lang is not write-able. Please contact your hosting provider": "", "File: :file_name is not write-able. Please contact your hosting provider": "", "Re-build language file for: :name success": "", "Default language source does not exists": "", "Default language source empty": "", "Default language source do not have any strings": "", "Loaded :count strings": "", "Generate Default JSON Language": "", "File language source does not exists": "", "File language source empty": "", "File language source do not have any strings": "", "Load language from json success": "", "Add new location": "", "Language Content": "", "Locale": "", "Flag Icon": "", "Eg: gb": "", "Please input flag code": "", "Display Name": "", "Please input language name": "", "Add Language": "", "All Languages": "", "Translate Manager for: :name": "", "All text": "", "Not translated": "", "Translated": "", "Search By": "", "Original Text": "", "Translated Text": "", "Search by key ...": "", "Filter": "", "Found :total texts": "", "Translate": "", "Origin": "", "Find Translations": "", "After translation. You must re-build language file to apply the change": "", "Last build at": "", "Build": "", "Booking Core by": "", "https://www.bookingcore.co": "", "BookingCore Team": "", "About Us": "", "https://m.me/bookingcore": "", "Contact Us": "", "Please check the form below for errors": "", "Do you want to restore?": "", "Confirm": "", "Custom Range": "", "W": "", "Su": "", "Mo": "", "Tu": "", "We": "", "Th": "", "Fr": "", "Sa": "", "January": "", "February": "", "March": "", "April": "", "May": "", "June": "", "July": "", "August": "", "September": "", "October": "", "November": "", "December": "", "Image Editor": "", "Toggle fullscreen": "", "Close window": "", "Save": "", "Save As New Image": "", "Go Back": "", "Adjust": "", "Effects": "", "Filters": "", "Orientation": "", "Crop": "", "Resize": "", "Watermark": "", "Focus point": "", "Shapes": "", "Brightness": "", "Contrast": "", "Exposure": "", "Saturation": "", "Rotate Left": "", "Rotate Right": "", "Flip Horizontally": "", "Flip Vertically": "", "Would you like to reduce resolution before editing the image?": "", "Keep original resolution": "", "Resize & Continue": "", "Reset": "", "Undo": "", "Redo": "", "Processing...": "", "The resolution of the image is too big for the web. It can cause problems with Image Editor performance.": "", "x": "", "y": "", "width": "", "height": "", "custom": "", "original": "", "square": "", "Opacity": "", "Apply watermark": "", "Upload": "", "Home": "", "Upgrade": "", "Mark all as read": "", "Notifications": "", "View More": "", "Edit Profile": "", "Vendor Dashboard": "", "Logout": "", "Email address": "", "Remember me": "", "Forgot Password?": "", "or continue with": "", "Do not have an account?": "", "Reset Password": "", "E-Mail Address": "", "Send Password Reset Link": "", "Confirm Password": "", "I have read and accept the <a href=':link' target='_blank'>Terms and Privacy Policy</a>": "", " Already have an account?": "", "Log In": "", "Register": "", "Verify Your Email Address": "", "A fresh verification link has been sent to your email address.": "", "Before proceeding, please check your email for a verification link.": "", "If you did not receive the email": "", "click here to request another": "", "We use cookies!": "", "Reject all": "", "Get Updates & More": "", "Thoughtful thoughts to your inbox": "", "Your Email": "", "Subscribe": "", "Hi, :Name": "", "My profile": "", "Messages": "", "Change password": "", "Admin Dashboard": "", "Hi, :name": "", "Credit: :amount": "", "My plan": "", "Support Center": "", "Location updated": "", "Location created": "", "Service Type": "", "Style 4": "", "List Location by IDs": "", "Link to location detail page?": "", "List Locations": "", "Location Category": "", "All Location": "", "All Category": "", "Icon class": "", "Location Categories": "", "Location Content": "", "Trip Ideas": "", "Title/Link": "", "Title:": "", "Link:": "", "Location name": "", "Add Location": "", "Location Map": "", "Click onto map to place Location address": "", "Media Management": "", "Please log in": "", "Can not remove!": "", "Please select file": "", "You don't have permission delete the file!": "", "Delete the file success!": "", "File not found!": "", "403": "", "You are not allowed to edit this folder": "", "Folder name exists, please select new one": "", "You are not allowed to delete this folder": "", "Folder deleted": "", "Can not upload the file": "", "File type are not allowed": "", "Maximum upload file size is :max_size B": "", "Can not get image dimensions": "", "Maximum width allowed is: :number": "", "Maximum height allowed is: :number": "", "Upload image": "", "Select images": "", "Can not edit non-local images": "", "Update Successful": "", "Media": "", "File type invalid": "", "Can not get image size": "", "Media Settings": "", "Folder not found. Please try again": "", "Can not upload file": "", "Search file name....": "", "files": "", "Add Folder": "", "No file found": "", "Previous": "", "Next": "", "Delete file": "", "file selected": "", "unselect": "", "Use file": "", "Cloud Storage Configs": "", "Select Cloud Driver": "", "-- Local Storage --": "", "AWS S3": "", "Google Cloud Storage": "", "Amazon S3": "", "Key": "", "Secret access key": "", "Default region": "", "Bucket": "", "Project ID": "", "Service Account Key File Name": "", "View file": "", "Folder": "", "Delete this folder": "", "Category updated": "", "Category created": "", "Please select an Action!": "", "News Management": "", "Add News": "", "News updated": "", "News created": "", "News does not exists": "", "Language does not exists": "", "Tag": "", "Tag updated": "", "Tag Created": "", "News: List Items": "", "Search results : \":s\"": "", "News Tag": "", "New Tag": "", "All News": "", "Tags": "", "Manage News": "", "News Settings": "", "Permalink:": "", "News Categories": "", "Search Category": "", "Edit post: ": "", "Add new Post": "", "View Post": "", "News content": "", "Enter tag": "", "All news": "", " Move to Pending ": "", "--All Category --": "", "Search News": "", "Page List": "", "Config page list news of your website": "", "Posts Per Page": "", "Sidebar Options": "", "Config sidebar for news": "", "Title: About Us": "", "Search Form": "", "Recent News": "", "Featured Listings": "", "Content Text": "", "Vendor News": "", "Config for vendor": "", "Admin need approve news to be publish": "", "Tag Content": "", "Tag name": "", "Tag Slug": "", "News Tags": "", "Add Tag": "", "Search keyword ...": "", "Search Tag": "", "Page Management": "", "Pages": "", "Add Page": "", "Edit Page": "", "Page updated": "", "Page created": "", "Header Style": "", "Transparent": "", "Add new page": "", "Permalink: ": "", "Template Builder": "", "View page": "", "Page Content": "", "All Page": "", "Search Page": "", "Popups": "", "Popup Management": "", "Recovery Popup Management": "", "Add Popup": "", "Add new Popup": "", "Edit Popup": "", "Popup updated": "", "Popup created": "", "Popup": "", "Add new popup": "", "Preview": "", "All Popups": "", "No popup found": "", "Show on": "", "Include URLs": "", "Wildcard allowed. Eg: */checkout/* ": "", "Exclude URLs": "", "Popup name": "", "Schedule": "", "Show every": "", "Day": "", "Month": "", "Year": "", "Property": "", "Properties": "", "Property Management": "", "Add Property": "", "Add new Property": "", "Edit Property": "", "Property updated": "", "Property created": "", "Contact property": "", "Title Link More": "", "Link More": "", "Background Color - get code in <a href=\"https://html-color-codes.info\" target=\"_blank\">https://html-color-codes.info</a>": "", "Style 1 : Background Color Only": "", "Style 2 : Background Image": "", "Style 3 : Background Image + Color": "", "Call To Action": "", "Property: Form Search": "", "Layout": "", "Carousel Layout": "", "View All Option": "", "View All With 3 Items Grid": "", "Hide button scroll down?": "", "Property: List Items": "", "Filter by Role": "", "User: List Users": "", "Select term property": "", "Property: Term Featured Box": "", "Sub title": "", "Number star": "", "Position": "", "List Testimonial": "", "Properties Availability": "", "Property not found": "", "Manage Properties": "", "Create Properties": "", "Property not found!": "", "Edit Properties": "", "Delete property success!": "", "Manage Property": "", "Property clone was successful": "", "Showing :from - :to of :total properties": "", "Search for Properties": "", "Maximum guests is :count": "", "This property is not available at selected dates": "", ":number Properties": "", ":number Property": "", "From ": "", "For Buy": "", "For Rent": "", "Property Category": "", "All Properties": "", "Property Settings": "", "Property Attributes": "", "Properties Availability Calendar": "", "No properties found": "", "Property Categories": "", "Name of property": "", "Name of vendor": "", "Add new property": "", "View Property": "", "Property type": "", "For buy": "", "For rent": "", "Property Featured": "", "Sold": "", "Sold Out": "", "No property found": "", "Property Content": "", "Video Background": "", "No. Bed": "", "No. Bathroom": "", "Square": "", "Example: 100": "", "Garages": "", "Year built": "", "Example: 2020": "", "Area": "", "Additional details": "", "Deposit": "", "Pool size": "", "Additional zoom": "", "Remodal year": "", "Amenities": "", "Equipment": "", "Property Price": "", "Property Sale Price": "", "Max Guests": "", "Discount by number of people": "", "No of people": "", "Discount": "", "Percent (%)": "", "Property Type": "", "Bathrooms": "", "Bedrooms": "", "Year Built": "", "Keyword": "", "-- Select Size --": "", "Page list properties layout": "", "Display Type": "", "Add prefix Price in Property listing?": "", "Open gallery when clicking Featured image on the Listing page?": "", "Layout Map Position": "", "Layout Map Size": "", "Page Detail": "", "Config page detail property of your website": "", "Page single property layout": "", "Config review for property": "", "Enable review system for Property?": "", "Turn on the mode for reviewing property": "", "Agent config for property": "", "Property created by vendor must be approved by admin": "", "Property information": "", "Property name": "", "All Bookings": "", "No items selected": "", "Please select action": "", "Search results: \":s\"": "", "Enquiry Management": "", "Enquiry :name": "", "All Replies": "", "Replies": "", "Reply added": "", "Reports :count": "", "Enquiry Reports": "", "Booking Reports": "", "Booking Statistic": "", "Credit Purchase Report :count": "", "-- Bulk Actions --": "", "Mark as: :name": "", "DELETE booking": "", "Search by name or ID": "", "Service": "", "Payment Information": "", "Commission": "", "by": "", "Name:": "", "Email:": "", "Phone:": "", "Address:": "", "Custom Requirement:": "", "Detail": "", "Set Paid": "", "Email Preview": "", "Booking ID: #": "", "All Enquiries": "", "DELETE Enquiry": "", "Search by email": "", "Enquiries": "", "Notes:": "", "Reply": "", "All Reply": "", "Add Reply": "", "Client Message:": "", "Content:": "", "Reply Content": "", "Add New": "", "Recent updates": "", "Bookings Statistic": "", "Filter:": "", "-- User Type --": "", "Customer User": "", "Vendor User": "", "Detail statistics": "", "Review": "", "Review not enable": "", "You need to make a booking or the Orders must be confirmed before writing a review": "", "You cannot review your service": "", "Review Title is required field": "", "Review Content is required field": "", "Review Content has at least 10 character": "", "Review success!": "", "Review success! Please wait for admin approved!": "", "Review error!": "", "Excellent": "", "Very Good": "", "Average": "", "Poor": "", "Terrible": "", "Review Advanced Settings": "", "All Reviews": "", " Approved ": "", " Pending ": "", " Spam ": "", " Move to Trash ": "", "-- Customer --": "", "Search by title": "", "Approved": "", "Spam": "", "Trash": "", "Review Content": "", "In Response To": "", "Submitted On": "", "More info": "", "View :name": "", "Allow customer upload picture to review": "", "Based on": "", ":number reviews": "", ":number review": "", "Showing :from - :to of :total total": "", "No Review": "", "Write a review": "", "Review title is required": "", "Review content": "", "Review content has at least 10 character": "", "Review rate": "", "Add photo": "", "Leave a Review": "", "You must <a href='#login' data-toggle='modal' data-target='#login'>log in</a> to write review": "", "Sms Settings": "", "Social": "", "Forum": "", "forum saved": "", "News Feed": "", "Forums": "", "Add new post": "", "How are you feeling today?": "", "Share": "", "Like": "", "Comments": "", "Space": "", "Spaces": "", "Space Management": "", "Recovery Space Management": "", "Add Space": "", "Add new Space": "", "Edit Space": "", "Space updated": "", "Space created": "", "Space: Form Search": "", "Service Space": "", "Space: List Items": "", "Space: Term Featured Box": "", "Select term space": "", "Spaces Availability": "", "Space not found": "", "Manage Spaces": "", "Recovery Spaces": "", "Restore space success!": "", "Create Spaces": "", "Edit Spaces": "", "Delete space success!": "", "Space clone was successful": "", ":count spaces found": "", ":count space found": "", "Showing :from - :to of :total Spaces": "", "This space is not available at selected dates": "", "You must to book a minimum of :number nights": "", ":number Spaces": "", ":number Space": "", "All Spaces": "", "Manage Space": "", "Space Settings": "", "Space Attributes": "", "Spaces Availability Calendar": "", "Showing :from - :to of :total spaces": "", "No spaces found": "", "Add new space": "", "View Space": "", "Space Featured": "", "No space found": "", "Config review for space": "", "Enable review system for Space?": "", "Turn on the mode for reviewing space": "", "Customer must book a space before writing a review?": "", "Config Booking for space": "", "Space by day": "", "Space by night": "", "Vendor config for space": "", "Space created by vendor must be approved by admin": "", "Disable space module?": "", "Disable space module": "", "Space Content": "", "Space Price": "", "Space Sale Price": "", "Space information": "", "Space name": "", "Support Settings": "", "Support Options": "", "Ticket Options": "", "Ticket Assign To": "", "Supporter View Type": "", "Per user [Default]": "", "Supporter see all": "", "Live Editor": "", "Template not found!": "", "Template can\\'t export. Please try again": "", "Import template ' . @$dataInput['title'] . ' success!": "", "Only support json file": "", "Your template has been saved": "", "Style Normal": "", "- Layout Normal: Background Color - get code in <a href=\"https://html-color-codes.info\" target=\"_blank\">https://html-color-codes.info</a>": "", "- Layout 2&3 : Background Image Uploader": "", "Client Feedback": "", "Column": "", "FAQ List": "", "Question": "", "Answer": "", "Form Search All Service": "", "Title for :service": "", "Slider Carousel Ver 2": "", "Background Video": "", "- Layout Video: Youtube Url": "", "Title (using for slider ver 2)": "", "Desc (using for slider ver 2)": "", "Hide form search service?": "", "How It Works": "", "Image Uploader": "", "Style 5": "", "List Featured Item": "", "Offer Block": "", "Featured text": "", "Featured icon (find icon class in : https://icofont.com/icons)": "", "Section": "", "Editor": "", "Wrapper Class (opt)": "", "Padding": "", "Video Player": "", "Youtube link": "", "Template": "", "Edit Template:": "", "Create new template": "", "Template Name": "", "Search for block...": "", "Template Content": "", "You need to create the template at the Main-language tab first!": "", "Save Template": "", "Are you want to delete?": "", "Import Template": "", "All Templates": "", "Choose file": "", "Import": "", "Template Management": "", "Import new Template": "", "Add new Template": "", "All templates": "", "Export": "", "Clone": "", "Last saved:": "", "Save Block": "", "ADD LAYER": "", "Search block ...": "", "LAYERS": "", "Add layer": "", "Theme management": "", "Theme Upload": "", "Disable for demo mode": "", "Theme activated": "", "DEMO MODE: You are not allowed to do that": "", "This theme does not have seeder class": "", "Demo data has been imported": "", "Can not run data import": "", "Themes": "", "All Themes": "", "Do you want to import all demo data?": "", "Import Demo Data": "", "Last run: :date": "", "Activate": "", "Upload Theme": "", "Select theme file": "", "Select theme zip file:": "", "Maximum file size is: ": "", "Upload Now": "", "Tour": "", "Attributes: :name": "", "Term not found!": "", "Tours": "", "Booking": "", "Tour Booking History": "", "Tour Management": "", "Recovery Tour Management": "", "Add Tour": "", "Edit Tour": "", "Tour updated": "", "Tour created": "", "Select Category": "", "Image Background": "", "Service Tour": "", "Tour: Box Category": "", "Tour: Form Search": "", "Box Shadow": "", "Slider Carousel Simple": "", "Tour: List Items": "", "Tours Availability": "", "Tour not found": "", "Max guests: ": "", "Manage Tours": "", "Recovery Tours": "", "Restore tour success!": "", "Create Tours": "", "Tour not found!": "", "Edit Tours": "", "Delete tour success!": "", "Tour clone was successful": "", ":count tours found": "", ":count tour found": "", "Showing :from - :to of :total Tours": "", "Search for Tours": "", "There are :maxGuests guests available for your selected date": "", "This tour is not available at selected dates": "", "This tour is not open on your selected day": "", "There are :numberGuestsCanBook guests available for your selected date": "", "Not Rated": "", ":number Tours": "", ":number Tour": "", "Tour Type": "", "Tour Category": "", "All Tours": "", "Booking Calendar": "", "Manage Tour": "", "Tour Settings": "", "Tour Attributes": "", "Tours Availability Calendar": "", "No tours found": "", "Max Guest": "", "Min": "", "Max": "", "Tour Booking Calendar": "", "Tour Filters": "", "Showing :from - :to of :total Tour(s)": "", "Tour Categories": "", "Add new tour": "", "View Tour": "", "Tour Information": "", "SEO": "", "Tour Featured": "", "All Tour": "", "-- All Category --": "", "Config review for tour": "", "Enable review system for Tour?": "", "Turn on the mode for reviewing tour": "", "Customer must book a tour before writing a review?": "", "Does the review need approved by admin?": "", "Config buyer fees for tour": "", "Vendor config for tour": "", "Tour create by vendor must be approved by admin?": "", "Disable tour module?": "", "Disable tour module": "", "Fixed dates": "", "Enable Fixed Date": "", "Start Date": "", "Last Booking Date": "", "Open Hours": "", "Enable Open Hours": "", "Enable?": "", "Day of Week": "", "Open": "", "Tuesday": "", "Wednesday": "", "Thursday": "", "Friday": "", "Saturday": "", "Itinerary": "", "Title - Desc": "", "Title: Day 1": "", "Desc: TP. HCM City": "", "Tour Price": "", "Tour Sale Price": "", "Person Types": "", "Enable Person Types": "", "Person Type": "", "Eg: Adults": "", "Minimum per booking": "", "Maximum per booking": "", "per 1 item": "", "Tour Content": "", "Tour Min People": "", "Tour Max People": "", "Tour Locations": "", "Real tour address": "", "Tour information": "", "Tour name": "", "Discounts:": "", "from :from guests": "", ":from - :to guests": "", "Tracking Report": "", "Select at leas 1 item!": "", "Deleted!": "", "Updated successfully!": "", "Tracking": "", "Tracking Settings": "", "-- Event Type --": "", "Phone Click": "", "Website Click": "", "Enquiry Click": "", "Email Ads Click": "", "Ads Name": "", "-- Service Type --": "", "Campaign": "", "Service ID": "", "Lang": "", "Ip": "", "Payout ID": "", "CPC": "", "Tracking System": "", "Config tracking system option": "", "Enable Tracking": "", "Yes,please enable it": "", "Do not track these IP address": "", "Example: *************, **************": "", "User Plans": "", "User Plan Management": "", "Edit user plan": "", "Plan saved": "", "Plan Report": "", "Plan request management": "", "Users": "", "Select at lease 1 item!": "", "Deleted successfully!": "", "Roles": "", "Role updated": "", "Edit Role": "", "DEMO Mode: You can not do this": "", "Role created": "", "Role Management": "", "Verify Configs": "", "Field not found": "", "Edit field: :name": "", "Field created": "", "Field saved": "", "Permission Matrix": "", "Permission Matrix updated": "", "Subscribers": "", "Edit: :email": "", "Email exists": "", "Subscriber updated": "", "Edit User: #:id": "", "DEMO MODE: You can not change password!": "", "Your current password does not matches with the password you provided. Please try again.": "", "Password updated!": "", "Display name is a required field": "", "User updated": "", "User created": "", "Verify email successfully!": "", "Verify email cancel!": "", "Verification Request": "", "Verify request: :email": "", "User not found": "", "No verification field found": "", "Updated": "", "Add Credit": "", "Add credit for :name": "", ":amount credit added": "", "Credit purchase report": "", "Phone is required field": "", "Invoice": "", ":name send you message: :message": "", ":name send you file": "", "New Password cannot be same as your current password. Please choose a different password.": "", "Password changed successfully !": "", "Pricing Packages": "", "My Plan": "", "My plans": "", "This plan is not suitable for your role.": "", "This plan doesn't have annual pricing": "", "Please select payment gateway": "", "Purchased user package successfully": "", ":name - reviews from guests": "", "Reviews from guests": "", ":name - :type": "", ":type by :first_name": "", "Two Factor Authentication": "", "Profile": "", "The User name field is required.": "", "Thank you for subscribing": "", "You are already subscribed": "", "You have just done the become vendor request, please wait for the Admin's approved": "", "Request vendor success!": "", "Error. You can\\'t permanently delete": "", "Wishlist": "", "Service ID is required": "", "Service type is required": "", "Delete fail!": "", "Verification": "", "Update Verification Data": "", "The :name is required": "", "The :name path is required": "", "Verification data saved. Please wait for admin approval": "", "Verify code do not match": "", "Wallet": "", "Deposit option is not valid": "", "Deposit option amount is not valid": "", "Deposit option credit is not valid": "", "[:site_name] We updated your verification data": "", "[:site_name] Verify Register": "", "Verify Email Address": "", "[:site_name] Permanently Delete Account": "", "[:site_name] An user submitted verification data": "", "Vendor Registration Approved": "", "New Vendor Registration": "", "Business Name": "", "Address 2": "", "State": "", "Zip Code": "", "Your upgrade request has approved already": "", "Your has created a plan request": "", " has created a plan request": "", ":name has requested to become a vendor": "", " has been registered": "", ":name has requested a Credit Purchase : :amount": "", "Administrator has approved your Credit amount": "", "Your plan request has been approved": "", "Your plan request has been cancelled": "", " plan request has been approved": "", " plan request has been cancelled": "", "Your account information was verified": "", ":name has asked for verification": "", "Someone": "", "You have just gotten a new Subscriber": "", ":duration day": "", ":duration days": "", ":duration week": "", ":duration weeks": "", ":duration month": "", ":duration months": "", ":duration year": "", ":duration years": "", "week": "", "month": "", "year": "", "Users :count": "", "Role Manager": "", "Upgrade Request :count": "", "Verification Request :count": "", "User Plans :count": "", "Plan Request :count": "", "My Wallet": "", "Verifications": "", "Messages :count": "", "2F Authentication": "", "My Plans": "", "Wallet Settings": "", "User Settings": "", "User Plans Settings": "", "User Info": "", "Business name": "", "E-mail": "", "User name": "", "Phone Number": "", "Birthday": "", "Address Line 1": "", "Address Line 2": "", "Biographical": "", "Email Verified?": "", "Vendor Commission Type": "", "Disable Commission": "", "Vendor commission value": "", "Avatar": "", "Export to excel": "", "Verified": "", "Not Verified": "", "Verify email": "", "Email verified": "", "Old Password": "", "New password": "", "Re-Password": "", "Permission Content": "", "Add new permission": "", "All Permission": "", "Add Plan": "", "Plan Content": "", "name": "", "For Role": "", "Free": "", "Annual Price": "", "Duration Type": "", "Week": "", "Max Services": "", "Unlimited": "", "How many publish services user can post": "", "ID": "", "-- Select Employer --": "", " All Plan ": "", "Plan ID": "", "Plan Name": "", "Expiry": "", "Used/Total": "", "Expired": "", "Renew": "", "Mark as completed": "", "Mark as cancelled": "", "-- Status --": "", "-- User --": "", "Purchase logs": "", "Plan": "", "Name: :name": "", "Duration:  :duration_text": "", "Role Content": "", "Role Name": "", "Role Code": "", "Should be unique and letters only": "", "Add new role": "", "All Roles": "", "Manage Fields": "", "Add new field": "", "All Fields": "", "Icon": "", "For roles": "", "Required": "", "Edit Field: :name": "", "Edit verification field": "", "Field ID": "", "Field ID ": "", "Must be unique. Only accept letter and number, dash, underscore, without space": "", "Please enter field id and make sure it unique": "", "Field Name": "", "Please enter field name": "", "File attachment": "", "Multi files attachment": "", "Please enter field type": "", "For Roles?": "", "Please enter roles": "", "Is Required?": "", "Icon code": "", "Eg: fa fa-phone": "", "User Plans Options": "", "Config user plans page": "", "Enable User Plans": "", "Page Title": "", "Page Sub Title": "", "Sale Of Text": "", "Enable Multi User Plans": "", "Plan Request options": "", "Content email send to Customer or Administrator.": "", "New request plan": "", "Enable send email to Administrator?": "", "Subject": "", "Enable send email to customer?": "", "Update request plan": "", "Register Options": "", "Config register option": "", "Disable Registration?": "", "User Register Default Role": "", "Inbox System": "", "Config inbox option": "", "Allow customer can send message to the vendor on detail page": "", "Google reCapcha Options": "", "Config google recapcha for system": "", "Enable reCapcha Login Form": "", "Turn on the mode for login form": "", "Enable reCapcha Register Form": "", "Turn on the mode for register form": "", "Disable verification feature?": "", "Disable verification feature": "", "When two factor authentication feature is enabled, the user is required to input a six digit numeric token during the authentication process. This token is generated using a time-based one-time password (TOTP) that can be retrieved from any TOTP compatible mobile authentication application such as Google Authenticator.": "", "Content Email User Registered": "", "Content email send to Customer or Administrator when user registered.": "", "Email to customer content": "", "Content Email User Verify Registered": "", "Content email verify send to Customer when user registered.": "", "Enable must verify email when customer registered ?": "", "Content Email User Forgot Password": "", "Disable Wallet module?": "", "Disable wallet module": "", "Credit Options": "", "Credit exchange rate": "", "Exchange rate will be used in checkout page. Example: Credit * Exchange rate = Money": "", "Deposit Options": "", "Deposit type": "", "User input": "", "Select from lists": "", "Deposit rate": "", "Example: Money * Deposit rate = Credit": "", "Deposit lists": "", "Earn credit": "", "All amount will be in main currency": "", "New Credit Purchase Email Template": "", "Email for Admin": "", "Email for Customer": "", "Credit Purchase Updated Template": "", "Permanently delete account": "", "Permanently delete account will delete all services of that user and that user": "", "Content confirm": "", "Content Email Permanently delete account": "", "Content email verify send when user permanently deleted.": "", "To customer": "", "To admin": "", "Vendor Requests": "", "Role request": "", "Date request": "", "Date approved": "", "Approved By": "", "Approve": "", "Data": "", "Information": "", "Mark as verified": "", "Verification Requests": "", "All Verification": "", "View Verification": "", "View request": "", "Add credit": "", "Balance": "", "Credit Amount": "", "Add now": "", "Credit Purchase Report": "", "Hello :name": "", "You are receiving this email because we updated your vendor verification data.": "", "Not verified": "", "You can check your information here:": "", "View verification data": "", "Regards": "", "An user has been submit their verification data.": "", "You can approved the request here:": "", "You are receiving this email because we approved your vendor registration request.": "", "You can check your dashboard here:": "", "View dashboard": "", "Add new subscriber": "", "Subscriber Info": "", "Add Subscriber": "", "Search by name or email": "", "Payout Management": "", "Vendor Plans": "", "Plan created": "", "Vendor plan updated": "", "List Vendor": "", "Vendor Register Form": "", "Enquiry Report": "", "Enquiry not found!": "", "Payouts Management": "", "Vendor dashboard": "", "Payouts": "", "Your account information has been saved": "", "Sorry! No method available at the moment": "", "You does not select payout method or you need to enter account info for that method": "", "You don not have enough :amount for payout": "", "Minimum amount to pay is :amount": "", "Payout request has been created": "", "Can not create vendor message": "", "Team members": "", "Member does not exists": "", "You can not add yourself": "", "Request exists": "", "Request created": "", "A payout request has been updated": "", "Your payout request has been updated": "", "A vendor has been submitted a payout request": "", "Your payout request has been submitted": "", "A payout request has been deleted": "", "Your payout request has been deleted": "", "A payout request has been rejected": "", "Your payout request has been rejected": "", "Request join team": "", "Administrator has :action your PAYOUT request": "", ":name has sent a Payout request": "", "Initial": "", "Rejected": "", "Vendor Plan Meta": "", "User upgrade request": "", "Payouts :count": "", "Teams": "", "Vendor Settings": "", "Payout request management": "", "With selected:": "", "Bulk action": "", "Search by payout id": "", "Payout Method": "", "To admin:": "", "To vendor:": "", ":name to :info": "", "Payout request bulk action": "", "Pay date": "", "YYYY/MM/DD": "", "Note to vendor": "", "Please select at lease one item": "", "Do you want to delete those items?": "", "Status is empty": "", "Team Members": "", "Change your config vendor team members": "", "Team Member enable?": "", "Auto-approve team member request?": "", "Terms & Conditions": "", "Config Vendor": "", "Change your config vendor system": "", "Vendor Enable?": "", "Example value : 10 or 10.5": "", "Example: 10% commssion. Vendor get 90%, Admin get 10%": "", "Vendor Register": "", "Vendor Auto Approved?": "", "Vendor Role": "", "Vendor Profile": "", "Show vendor email in profile?": "", "Show vendor phone in profile?": "", "Payout Options": "", "Disable Payout Module?": "", "Booking Status Conditions": "", "Select booking status will be use for calculate payout of vendor": "", "Payout Methods": "", "Eg: bank_transfer": "", "Minimum to pay": "", "Content Email Vendor Registered": "", "Content email send to Vendor or Administrator when user registered.": "", "Email to vendor content": "", "Your payout request has been submitted:": "", "Your payout request has been updated:": "", "Your payout request has been rejected:": "", "Status:": "", "Pay date:": "", "Note to vendor:": "", "Payout information:": "", "Payout ID:": "", "Amount: ": "", "Payout method: ": "", "Note to admin: ": "", "Created at: ": "", "You can check your payout request here:": "", "View payouts": "", "Hello administrator": "", "A vendor has been submitted a payout request:": "", "A payout request has been updated:": "", "A payout request has been rejected:": "", "Vendor: ": "", "You can check all payout request here:": "", "Manage payouts": "", "Member Since :time": "", "View Profile": "", "All Booking": "", "Order Date": "", "Execution Time": "", "Payment Detail": "", "No Booking History": "", "Service Info": "", "Customer Info": "", "Vendor Payouts": "", "No payout methods available. Please contact administrator": "", "Payout history": "", "#": "", "Date Request": "", "Notes": "", "Date Processed": "", "Create request": "", "Balance: ": "", "Your balance is zero": "", "Create payout request": "", "Available for payout": "", "Method": "", "Minimum: :amount": "", "Note to admin": "", "Send request": "", "Setup your payment accounts": "", "Setup accounts": "", "To create payout request, please setup your payment account first": "", "Setup payout accounts": "", "Your account": "", "Your account info": "", "Vendor Teams": "", "As an author, you can add other users to your team. People on your team will be able to manage your services.": "", "Add someone to your team:": "", "Permissions": "", "Add": "", "Users on your team": "", "Send email": "", "Enable Two Checkout?": "", "Two Checkout": "", "Account Number": "", "Secret Word": "", "Gateway 2Checkout": "", "Gateway 2Checkout is one of the best payment Gateway to accept online payments from buyers around the world which allow your customers to make purchases in many payment methods, 15 languages, 87 currencies, and more than 200 markets in the world.": "", "Expiration Month": "", "Expiration Year": "", "Write a ": "", "OpenAI Settings": "", "API Key": "", "Model Name": "", "Magic text generator": "", "Keywords": "", "Some basic information or keywords": "", "Generate": "", "Use this content": "", "Booking status not valid": "", "Ticket not found": "", "This ticket does not belong to your events": "", "Ticket already scanned at :time": "", "Ticket scan success": "", "Ticket ID": "", "Show QR Code at the counter": "", "QR Code scanned at: :time": "", "Manage category": "", "Edit category": "", "Ticket Management": "", "Support does not exists": "", "Topic": "", "Support": "", "Topics": "", "Topic Management": "", "Duplicated": "", "Add Support": "", "Support updated": "", "Topic created": "", "All Tickets": "", "Search result for: :name": "", "My Tickets": "", "Create a ticket": "", "Ticket created": "", "Reply created": "", "Can not add reply": "", "All Topics": "", "New reply on ticket: #\" . $this->ticket->id))->view('Support::email.new_reply": "", "Topic Tag": "", "Closed": "", "Support Category": "", "Add Topic": "", "Edit topic: :name": "", "Add new topic": "", "All tickets": "", "Search topic": "", "Agent": "", "Unassigned": "", "View Ticket": "", "All topics": "", "Topic Tags": "", "Hello": "", "You got new reply for ticket: #": "", "Reply content:": "", "You can check the ticket here:": "", "View ticket": "", "View all": "", "Ticket Status": "", "Save Status": "", "User Notes": "", "Add note": "", "Add user note": "", "Old": "", "New": "", "Please provide ticket content": "", "Need Response": "", "Support tickets": "", "All categories": "", "How Can We Help?": "", "Find out more topics": "", "Support Tickets": "", "Search topic...": "", "Popular Topics": "", "Create new ticket": "", "Create ticket": "", "Ask a question": "", "Ticket name": "", "Last reply": "", "Showing :from - :to of :total tickets": "", "No ticket found": "", "Tags: ": "", "Related topics": "", "Showing :from - :to of :total topics": "", "No topic found": "", "These credentials do not match our records.": "", "Too many login attempts. Please try again in :seconds seconds.": "", "Laravel Installer": "", "Next Step": "", "Install": "", "The Following errors occurred:": "", "Welcome": "", "Easy Installation and Setup Wizard.": "", "Check Requirements": "", "Step 1 | Server Requirements": "", "Server Requirements": "", "Check Permissions": "", "Step 2 | Permissions": "", "Configure Environment": "", "Step 3 | Environment Settings": "", "Environment Settings": "", "Please select how you want to configure the apps <code>.env</code> file.": "", "Form Wizard Setup": "", "Classic Text Editor": "", "Step 3 | Environment Settings | Guided Wizard": "", "Guided <code>.env</code> Wizard": "", "Environment": "", "Database": "", "Application": "", "An environment name is required.": "", "App Name": "", "App Environment": "", "Local": "", "Development": "", "Qa": "", "Production": "", "Other": "", "Enter your environment...": "", "App Debug": "", "True": "", "False": "", "App Log Level": "", "debug": "", "info": "", "notice": "", "warning": "", "error": "", "critical": "", "alert": "", "emergency": "", "App Url": "", "Admin Password": "", "Database Connection": "", "mysql": "", "sqlite": "", "pgsql": "", "sqlsrv": "", "Database Host": "", "Database Port": "", "Database Name": "", "Database User Name": "", "Database Password": "", "More Info": "", "Broadcasting, Caching, Session, &amp; Queue": "", "Cache Driver": "", "Session Driver": "", "Queue Driver": "", "Redis Driver": "", "Redis Host": "", "Redis Password": "", "Redis Port": "", "Mail": "", "Mail Driver": "", "Mail Host": "", "Mail Port": "", "Mail Username": "", "Mail Password": "", "Mail Encryption": "", "Pusher": "", "Pusher App Id": "", "Pusher App Key": "", "Pusher App Secret": "", "Setup Database": "", "Setup Application": "", "Step 3 | Environment Settings | Classic Editor": "", "Classic Environment Editor": "", "Save .env": "", "Use Form Wizard": "", "Save and Install": "", "Your .env file settings have been saved.": "", "Unable to save the .env file, Please create it manually.": "", "Laravel Installer successfully INSTALLED on ": "", "Installation Finished": "", "Application has been successfully installed.": "", "Migration &amp; Seed Console Output:": "", "Application Console Output:": "", "Installation Log Entry:": "", "Final .env File:": "", "Click here to exit": "", "Laravel Updater": "", "Welcome To The Updater": "", "Welcome to the update wizard.": "", "Overview": "", "There is 1 update.|There are :number updates.": "", "Finished": "", "Application\\'s database has been successfully updated.": "", "Laravel Installer successfully UPDATED on ": "", "&laquo; Previous": "", "Next &raquo;": "", "Passwords must be at least eight characters and match the confirmation.": "", "Your password has been reset!": "", "This password reset token is invalid.": "", "Confirm password": "", "This is a secure area of the application. Please confirm your password before continuing.": "", "Please confirm access to your account by entering one of your emergency recovery codes.": "", "Recovery Code": "", "Please confirm access to your account by entering the authentication code provided by your authenticator application.": "", "Use an authentication code": "", "Use a recovery code": "", "Unauthorized": "", "Forbidden": "", "Page not found": "", "Sorry, we couldn't find the page you're looking for.": "", "Page Expired": "", "Too Many Requests": "", "Server Error": "", "Service Unavailable": "", "Oh no": "", "Go Home": "", "installer_messages.environment.classic.templateTitle": "", "installer_messages.environment.classic.title": "", "installer_messages.environment.classic.save": "", "installer_messages.environment.classic.back": "", "installer_messages.environment.classic.install": "", "installer_messages.environment.wizard.templateTitle": "", "installer_messages.environment.wizard.title": "", "Site Name": "", "installer_messages.environment.wizard.form.app_name_placeholder": "", "installer_messages.environment.wizard.form.app_environment_label": "", "installer_messages.environment.wizard.form.app_environment_label_local": "", "installer_messages.environment.wizard.form.app_environment_label_developement": "", "installer_messages.environment.wizard.form.app_environment_label_qa": "", "installer_messages.environment.wizard.form.app_environment_label_production": "", "installer_messages.environment.wizard.form.app_environment_label_other": "", "installer_messages.environment.wizard.form.app_environment_placeholder_other": "", "installer_messages.environment.wizard.form.app_debug_label": "", "installer_messages.environment.wizard.form.app_debug_label_true": "", "installer_messages.environment.wizard.form.app_debug_label_false": "", "installer_messages.environment.wizard.form.app_log_level_label": "", "installer_messages.environment.wizard.form.app_log_level_label_debug": "", "installer_messages.environment.wizard.form.app_log_level_label_info": "", "installer_messages.environment.wizard.form.app_log_level_label_notice": "", "installer_messages.environment.wizard.form.app_log_level_label_warning": "", "installer_messages.environment.wizard.form.app_log_level_label_error": "", "installer_messages.environment.wizard.form.app_log_level_label_critical": "", "installer_messages.environment.wizard.form.app_log_level_label_alert": "", "installer_messages.environment.wizard.form.app_log_level_label_emergency": "", "Site url": "", "installer_messages.environment.wizard.form.app_url_placeholder": "", "installer_messages.environment.wizard.form.buttons.setup_database": "", "installer_messages.environment.wizard.form.db_connection_label": "", "installer_messages.environment.wizard.form.db_connection_label_mysql": "", "installer_messages.environment.wizard.form.db_connection_label_sqlite": "", "installer_messages.environment.wizard.form.db_connection_label_pgsql": "", "installer_messages.environment.wizard.form.db_connection_label_sqlsrv": "", "installer_messages.environment.wizard.form.db_host_label": "", "installer_messages.environment.wizard.form.db_host_placeholder": "", "installer_messages.environment.wizard.form.db_port_label": "", "installer_messages.environment.wizard.form.db_port_placeholder": "", "installer_messages.environment.wizard.form.db_name_label": "", "installer_messages.environment.wizard.form.db_name_placeholder": "", "installer_messages.environment.wizard.form.db_username_label": "", "installer_messages.environment.wizard.form.db_username_placeholder": "", "installer_messages.environment.wizard.form.db_password_label": "", "installer_messages.environment.wizard.form.db_password_placeholder": "", "installer_messages.environment.wizard.form.app_admin_email_placeholder": "", "installer_messages.environment.wizard.form.app_admin_password_placeholder": "", "Test DB": "", "installer_messages.environment.wizard.form.buttons.install": "", "installer_messages.environment.wizard.form.app_tabs.broadcasting_title": "", "installer_messages.environment.wizard.form.app_tabs.broadcasting_label": "", "installer_messages.environment.wizard.form.app_tabs.more_info": "", "installer_messages.environment.wizard.form.app_tabs.broadcasting_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.cache_label": "", "installer_messages.environment.wizard.form.app_tabs.cache_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.session_label": "", "installer_messages.environment.wizard.form.app_tabs.session_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.queue_label": "", "installer_messages.environment.wizard.form.app_tabs.queue_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.redis_label": "", "installer_messages.environment.wizard.form.app_tabs.redis_host": "", "installer_messages.environment.wizard.form.app_tabs.redis_password": "", "installer_messages.environment.wizard.form.app_tabs.redis_port": "", "installer_messages.environment.wizard.form.app_tabs.mail_label": "", "installer_messages.environment.wizard.form.app_tabs.mail_driver_label": "", "installer_messages.environment.wizard.form.app_tabs.mail_driver_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.mail_host_label": "", "installer_messages.environment.wizard.form.app_tabs.mail_host_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.mail_port_label": "", "installer_messages.environment.wizard.form.app_tabs.mail_port_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.mail_username_label": "", "installer_messages.environment.wizard.form.app_tabs.mail_username_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.mail_password_label": "", "installer_messages.environment.wizard.form.app_tabs.mail_password_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.mail_encryption_label": "", "installer_messages.environment.wizard.form.app_tabs.mail_encryption_placeholder": "", "installer_messages.environment.wizard.form.app_tabs.pusher_label": "", "installer_messages.environment.wizard.form.app_tabs.pusher_app_id_label": "", "installer_messages.environment.wizard.form.app_tabs.pusher_app_id_palceholder": "", "installer_messages.environment.wizard.form.app_tabs.pusher_app_key_label": "", "installer_messages.environment.wizard.form.app_tabs.pusher_app_key_palceholder": "", "installer_messages.environment.wizard.form.app_tabs.pusher_app_secret_label": "", "installer_messages.environment.wizard.form.app_tabs.pusher_app_secret_palceholder": "", "installer_messages.environment.menu.templateTitle": "", "installer_messages.environment.menu.title": "", "installer_messages.environment.menu.desc": "", "installer_messages.environment.menu.wizard-button": "", "installer_messages.environment.menu.classic-button": "", "installer_messages.final.templateTitle": "", "installer_messages.final.title": "", "installer_messages.final.log": "", "installer_messages.final.exit": "", "installer_messages.updater.title": "", "installer_messages.title": "", "installer_messages.forms.errorTitle": "", "installer_messages.permissions.templateTitle": "", "installer_messages.permissions.title": "", "installer_messages.permissions.next": "", "installer_messages.requirements.templateTitle": "", "installer_messages.requirements.title": "", "installer_messages.requirements.next": "", "installer_messages.updater.final.title": "", "installer_messages.updater.final.exit": "", "installer_messages.updater.welcome.title": "", "installer_messages.updater.overview.message": "", "installer_messages.updater.overview.install_updates": "", "installer_messages.updater.welcome.message": "", "installer_messages.next": "", "installer_messages.welcome.templateTitle": "", "Booking Core :version Installer": "", "installer_messages.welcome.message": "", "installer_messages.welcome.next": "", "Log file >50M, please download it.": "", "Context": "", "Line number": "", "pagination.previous": "", "pagination.next": "", "Booking ID": "", "Booking Detail": "", "Customer Information": "", "Your Booking": "", "Start date:": "", "Total:": "", "Paid:": "", "Remain:": "", "End date": "", "Durations": "", "Details": "", "1. Content": "", "2. Locations": "", "3. Pricing": "", "4. Attributes": "", "No Boat": "", "Last Updated": "", "\"Do you want to recovery?\"": "", "\"Do you want to permanently delete?\"": "", "Del": "", "\"Do you want to delete?\"": "", "Make hide": "", "Make publish": "", "Boat by :name": "", "View all (:total)": "", "You got reply from vendor. ": "", "Service:": "", "Your note:": "", "Here is the message from vendor:": "", "New booking has been made": "", "Your service has new booking": "", "Thank you for booking with us. Here are your booking information:": "", "Customer information": "", "Tickets / Guests Information:": "", "Guest #:number": "", "First Name: ": "", "Last Name: ": "", "Email: ": "", "Phone: ": "", "The booking status has been updated": "", "Personal Information": "", "Guests Information": "", "Number:": "", "5. Ical": "", "No Car": "", "Car by :name": "", "100$": "", "Bonus 10%": "", "Bonus 15%": "", "only for Services": "", "Manage Coupon": "", "Add Coupon": "", "Showing :from - :to of :total coupon": "", "No Coupon": "", "Print Ticket": "", "Event by :name": "", ":from to :to": "", ":duration hrs": "", "Arrival Time ": "", "No Flight": "", "Do you want to permanently delete?": "", "Add new seat": "", "Manage Seats": "", "1. Seat Content": "", "Back to flight": "", "Add Seat": "", "Flight id": "", "Flight by :name": "", "Check in:": "", "Adults:": "", "Children:": "", "Check out": "", "Hotel by :name": "", "Recovery news": "", "Manage news": "", "No Space": "", "Space by :name": "", "There is no layer yet!": "", "Click button bellow to start adding layer": "", "4. Availability": "", "5. Attributes": "", "6. Ical": "", "No Tours": "", "Tour by :name": "", "Setup Two Factor Authentication": "", "You have enabled factor authentication": "", "When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application.": "", "Two factor authentication is now enabled. Scan the following QR code using your phone's authenticator application.": "", "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.": "", "Disable two factor authentication": "", "You have not enabled factor authentication": "", "Enable now": "", "Two-factor authentication adds an additional layer of security to your account by requiring more than just a password to sign in": "", "INVOICE": "", "Invoice #: :number": "", "Created: :date": "", "Amount due:": "", "Billing to:": "", "Current Password": "", "New Password": "", "* Require at least one uppercase, one lowercase letter, one number and one symbol.": "", "New Password Again": "", "My Profile": "", "Become a vendor": "", "Log Out": "", "Back to Homepage": "", "Choose your pricing plan": "", "Save up to 10%": "", "Monthly": "", "Annual": "", "Recommended": "", "Current Plan": "", "Repurchase": "", "Select": "", "My Current Plan": "", "No Items": "", "Hi, I'm :name": "", "View all reviews (:total)": "", ":count review": "", ":count reviews": "", "About Yourself": "", "Browse": "", "Error upload...": "", "No Image": "", "Location Information": "", "Address2": "", "Delete account": "", "Your account will be permanently deleted. Once you delete your account, there is no going back. Please be certain.": "", "Delete your account": "", "Confirm permanently delete account": "", "Select File": "", "N/A": "", "Select Files": "", "Verify Phone": "", "Verification data": "", "Update verification data": "", "Verify": "", "Buy": "", "Sorry, no options found": "", "How much would you like to deposit?": "", "Deposit amount": "", "Process now": "", ":amount": "", "Latest Transactions": "", "Gateway": "", "Deposit by :name": "", "WishList": "", "Showing :from - :to of :total": "", ":number Reviews": "", ":number Review": "", "Remove": "", "Advanced Filter": "", "Customer name": "", "Customer Name": "", "From - To": "", "We couldn't find any boats.": "", "Try changing your filter criteria": "", "Please select start date": "", "Please select at least one number": "", "Name is Required": "", "Email is Required": "", "Boat Video": "", "from :number reviews": "", ":number% of guests recommend": "", "Length Boat": "", "from": "", "/per hour": "", "/per day": "", "Book Now": "", "Contact Now": "", "Book": "", "Return on same-day": "", "Return on another day": "", "Days": "", "Select Dates": "", "Book :number days in advance": "", "Book :number day in advance": "", "Extra prices:": "", "BOOK NOW": "", "Included": "", "Excluded": "", "Included/Excluded": "", "You might also like": "", "Specs & Details": "", "All :name": "", "Where are you going?": "", "Search for...": "", "FILTER BY": "", "APPLY": "", "Sort by:": "", "Price (Low to high)": "", "Price (High to low)": "", "Rating (High to low)": "", "Apply Filters": "", "Price filter": "", "More filters": "", "Availability Boats": "", "We couldn't find any cars.": "", "Clear Filters": "", "Please select Start and End date": "", "Car Video": "", "Stay at least :number days": "", "Stay at least :number day": "", "/day": "", "Availability Cars": "", "Header Settings": "", "Enable Header Sticky": "", "We couldn't find any events.": "", "Event Video": "", "People interest: :number": "", "per ticket": "", "Start Time: :time": "", "Availability Events": "", "No Event": "", "Flight not found": "", "Please select at least one guest": "", "View on map": "", "People": "", "bathrooms": "", "beds": "", "Ages 12+": "", "Ages 2–12": "", ":count Guest in maximum": "", ":count Guests in maximum": "", "You might also like...": "", "Video": "", "1 Adult": "", ":count Adults": "", ":count Child": "", ":count Children": "", "City or airport": "", ":count '.$type->name)}}\">{{__(':count'.$type->name,['count'=>$inputValue??$minValue])}}</span>\r\n                        </span>\r\n\t\t\t\t\t@endforeach\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"dropdown-menu select-seat-type-dropdown\" >\r\n\t\t\t@foreach($seatType as $type)\r\n\t\t\t\t<?php\r\n\t\t\t\t$inputName = 'seat_type_'.$type->code;\r\n\t\t\t\t$inputValue = $seatTypeGet[$type->code] ?? $minValue;\r\n\t\t\t\t;?>\r\n\r\n\t\t\t\t<div class=\"dropdown-item-row\">\r\n\t\t\t\t\t<div class=\"label\">{{__('Adults :type": "", "avg/person": "", "Take off": "", "Landing": "", "Choose": "", "Check-in": "", "Pay Amount": "", "We couldn't find any spaces.": "", "Availability Spaces": "", "We couldn't find any hotels.": "", "Hotel Video": "", "Rules": "", "Check In": "", "Check Out": "", "Hotel Policies": "", "Show All": "", "Related Hotel": "", "/night": "", "Available Rooms": "", "Check In - Out": "", "Check Availability": "", "Room Footage": "", "No. Beds": "", "No. Adults": "", "No. Children": "", "Total Room": "", "Total Price": "", "No room available with your selected date. Please change your search critical": "", "What's Nearby": "", "Availability Rooms": "", "No Hotel": "", "Add new room": "", "1. Room Content": "", "2. Pricing": "", "3. Attributes": "", "4. Ical": "", "Showing :from - :to of :total Rooms": "", "No Room": "", "Upgrade to PRO to unlock unlimited access to all of our features": "", "Explore the place": "", "The City Maps": "", "FEATURED ARTICLE": "", "Read More": "", "Showing :from - :to of :total posts": "", "Sorry, but nothing matched your search terms. Please try again with some different keywords.": "", "BY ": "", "DATE ": "", "Tags:": "", "Search ...": "", "Oops! It looks like you're lost.": "", "The page you're looking for isn't available. Try to search again or use the go to.": "", "Go back to homepage": "", "Space Video": "", "No. People": "", "We couldn't find any tours.": "", "Book now": "", "Please select Start date": "", "Tour Video": "", "Group Size": "", ":number persons": "", ":number person": "", "Tour Location": "", "Tour Start Date": "", "Tour End Date": "", "per person": "", "Message host": "", "Availability Tours": "", "Showing :from - :to of :total tours": "", "Enable Preload": "", "Logo Dark": "", "Page 404 settings": "", "Settings for 404 error page": "", "Error 404 banner": "", "Error title": "", "Error desc": "", "FEATURE": "", ":count enrolled on this course": "", "Last updated :date_update": "", "Add To Cart": "", "Buy Now": "", "Lessons": "", "Quizzes": "", "Skill level": "", "Instructor": "", "Instructor Rating": "", "You May Like": "", "10,000+ unique online course list designs": "", "Expand All Sections": "", "Show more": "", "All Levels": "", "5": "", "4.0 & up": "", "3.0 & up": "", "2.0 & up": "", "1.0 & up": "", "FEATURED": "", "lesson": "", "lessons": "", "Showing": "", "total results": "", "Newest": "", "Oldest": "", "Price [high to low]": "", "Price [low to high]": "", "Rating [high to low]": "", "Explore": "", "Log in": "", "Call us": "", "Sign up": "", "My Courses": "", "Popular Right Now": "", "PRESS ENTER TO SEE ALL SEARCH RESULTS": "", "Banner Sub Title": "", "Enable review system for News?": "", "Turn on the mode for reviewing news": "", "Fb": "", "Tw": "", "Linkedin": "", "Ln": "", "Pinterest": "", "Pin": "", "Prev": "", "Related Posts": "", "What are you looking for?": "", "All Categories": "", "Don't have an account yet?": "", "Sign up for free": "", "Already have an account?": "", "Go Back To Homepage": "", "Leave A Review": "", "Enter Title": "", "Rating": "", "Five Star": "", "Four Star": "", "Three Star": "", "Two Star": "", "One Star": "", "Submit Review": "", "reviews": "", "review": "", ":from - :to of :total+ :review available": "", "Agent Single": "", " Listings": "", "Mobile: ": "", "View My Listing": "", "Listing": "", "Your Name": "", "Your Message": "", "Search results": "", "Sort by": "", "Name ( a -> z )": "", "Name ( z -> a )": "", "Listings": "", "Advanced Search": "", "Hide Filter": "", "All Agents": "", "Find Agent": "", "Enter Agent Name": "", "Agency": "", "(:rate_agv out of 5)": "", "Write a Review": "", "Your Rating & Review": "", "Min Area": "", "Max Area": "", "Advanced features": "", "Show Filter": "", "Agents": "", "Search Agent": "", "Create Agent": "", "Search Agency": "", "Send Message": "", "Become a Real Estate Agent": "", "We only work with the best companies around the globe": "", "Register Now": "", "Contact Information": "", "Logo Transparent": "", "Logo Mobile": "", "Contact Title": "", "Contact Sub Title": "", "Contact Banner": "", "Enable reCapcha Form": "", "Turn on the mode for contact form": "", "Contact Locations": "", "Contact partners": "", "Contact partner title": "", "Contact partner sub title": "", "Contact partner button text": "", "Contact partner button link": "", "Views": "", "Total Views": "", "Total Visitor Reviews": "", "Favorites": "", "Total Favorites": "", "Recent Activities": "", "Login with Facebook": "", "Login with Google": "", "Login with Twitter": "", "Lost your password?": "", "I have read and accept the Terms and Privacy Policy?": "", "Already have an account? ": "", "Agent Dashboard": "", "Create Listing": "", "Dashboard Navigation": "", "© 2020 Find House. Made with love.": "", "View All": "", "views": "", "property": "", "properties": "", "/mo": "", "Beds:": "", "Baths:": "", "Sq Ft:": "", "Search Here": "", "Scroll Down": "", "to discover more": "", "Beds": "", "Baths": "", "Sq Ft": "", "Show More": "", "Property Details": "", "Property ID": "", "Property Size": "", "None": "", "Garage": "", "Property Status": "", "Pool Size": "", "Additional Rooms": "", "Last remodel year": "", "Listed By": "", "View Photos": "", "Similar Properties": "", "Hide\r\n                                    Filter": "", "List Property": "", ":count properties found": "", ":count property found": "", "Name [a->z]": "", "Name [z->a]": "", "Sq:": "", "Search by keyword": "", "All Type": "", "We couldn't find any properties.": "", "No Booking": "", "phone": "", "email": "", "Property Image": "", "Add property": "", "Search Properties": "", "Listing Title": "", "Date published": "", "Property by :name": "", "Featured Properties": "", "Recently Viewed": "", "Availability Properties": "", "Layout 1": "", "Layout 2": "", "- Style Slider: List Item(s)": "", "Select property": "", "Hide Slider Controls": "", "Banner Property": "", "Show Attribute": "", "Style 2 - Slider Carousel": "", "Style 6": "", "Style 7": "", "- Style: Background Image Uploader": "", "Video URL": "", "Gallery Images": "", "Video Url (Youtube, Vimeo, ..)": "", "Icon Class": "", "Image Text With Counting": "", "List Location by ID": "", "Property Map by location": "", "Style 2 : With Background Image": "", "Background Image Style 4": "", "Custom Title": "", "Background Image": "", "Page Banner": "", ", Baths:": "", ", Sq:": "", "Learn More": "", "Enter keyword ...": "", "Choose Price": "", "Hide": "", "Rent": "", "You have just done the become agent request, please wait for the Admin\\'s approved": "", "Manage Contacts": "", "--Select Filter--": "", "Property contact": "", "Agent contact": "", "Agency contact": "", "Object": "", "Guest name": "", "Created at": "", "') }}\r\n                                            @endif\r\n\r\n                                            @if ($row->object_model == 'property": "", "Property views": "", "Become a agent": "", "User Social": "", "Scroll Down ID": "", "List Layout": "", "Grid Layout": "", "About": "", "Sale off :number": "", "Starting from": "", "per adult": "", "View Detail": "", "Clear All": "", "Lorem ipsum dolor sit amet, consectetur.": "", "Sign in": "", "to book with your saved details or": "", "register": "", "to manage your bookings on the go!": "", "Let us know who you are": "", "List Car by IDs": "", "Map Background in Button Show Map": "", "Property highlights": "", "Select Number": "", "See All": "", "Show on map": "", "Send a message": "", "Full Name": "", "Your Messages": "", "Send a Messsage": "", "List Contact": "", "Info Contact": "", "Iframe google map": "", "Why Choose Us": "", "Block: Title": "", "Block: Desc": "", "List Items": "", "Title/Desc": "", "Footer Style": "", "Style 8": "", "Footer content left": "", "Footer content right": "", "Logo Preload": "", "Do you have a promo code?": "", "Enter promo code": "", "Select term event": "", "Event: Term Feature Box": "", "Term Icon": "", "Event snapshot": "", ":number% of travelers recommend this experience": "", "Start Time: :time - ": "", "Destinations": "", "Price Filter": "", "Service Flight": "", "Showing :from - :to of :total flights": "", "- List Item(s)": "", "Discover Title": "", "Discover Link": "", "Normal 2": "", "Slider Carousel V2": "", "Showing :from - :to of :total hotels": "", "Scroll Now": "", "Select Room": "", "Room Type": "", "Benefits": "", "Select Rooms": "", "Show Room Information": "", "adults": "", "children": "", "Hotel Rules - Policies": "", "See Availability": "", "Welcome back": "", "Sign In": "", "or sign in with": "", "By creating an account, you agree to our Terms of Service and Privacy Statement.": "", "Sign in or create an account": "", "FAQs about": "", "See All :count Photos": "", "Guest reviews": "", "Leave a Reply": "", "Your email address will not be published.": "", "Write Your Comment": "", "Post Comment": "", "You must <a href='#login' data-bs-toggle='modal' data-target='#login'>log in</a> to write review": "", ":count '.$type->name)}}\">{{__(':count'.$type->name,['count'=>$inputValue??$minValue])}}</span>\r\n                    </span>\r\n                @endforeach\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"searchMenu-guests__field select-seat-type-dropdown shadow-2\" data-x-dd=\"searchMenu-guests\" data-x-dd-toggle=\"-is-active\">\r\n        <div class=\"bg-white px-30 py-30 rounded-4\">\r\n            @foreach($seatType as $type)\r\n                <?php\r\n                $inputName = 'seat_type_'.$type->code;\r\n                $inputValue = $seatTypeGet[$type->code] ?? $minValue;\r\n                ?>\r\n\r\n                <div class=\"row y-gap-10 justify-between items-center\">\r\n                    <div class=\"col-auto\">\r\n                        <div class=\"text-15 fw-500\">{{__('Adults :type": "", "Where you’ll be": "", "Your Travel Journey Starts Here": "", "Sign up and we'll send the best deals to you": "", "Main Menu": "", "Become An Expert": "", "Sign In / Register": "", "MENU": "", "Style 9": "", "Style 10": "", "View All Url": "", "General info": "", "Desc:": "", "View All Destinations": "", "Discover": "", "travellers": "", "Explore deals, travel guides and things to do in :text": "", "Interdum et malesuada fames ac ante ipsum": "", "What to know before visiting :text": "", "Most Popular :name": "", "Top sights in  :text": "", "These popular destinations have a lot to offer": "", "See More": "", "Header Align": "", "Left": "", "Center": "", "Right": "", "Related content": "", "Interdum et malesuada fames": "", "Normal White": "", "Transparent V2": "", "Transparent V3": "", "Transparent V4": "", "Transparent V5": "", "Transparent V6": "", "Transparent V7": "", "Transparent V8": "", "Transparent V9": "", "Disable subscribe default": "", "Bathroom": "", "nights": "", "guests": "", ":count :guest": "", ":count bathroom": "", ":count bed": "", "per night": "", "About Text": "", "Subtitle": "", "Link Download": "", "Download App": "", "Normal Ver 2": "", "Slider Ver 2": "", "List All Service": "", "Login Register": "", "OverLay": "", "Subscribe Style": "", "List Terms": "", "Text Featured Box": "", "Text Image": "", "Image 1": "", "Image 2": "", "Style 4 (Only List Item)": "", "Style 5 (Only List Item)": "", "Youtube Image": "", "Youtube Link": "", "Job": "", "Happy people number": "", "Happy people text": "", "Overall rating number": "", "Overall rating text": "", "Overall rating star": "", "Title Trusted": "", "List Trusted(s)": "", "Logo Image": "", "Testimonial Background (For Style 4, Style 6, Style 7)": "", "Book Title": "", "Book Desc": "", "Book Url": "", "Book Url Text": "", "Book Image": "", "Tour: Tour Deals": "", "Tour: Tour Types": "", "Category Icon Class": "", "Category icon": "", "Watch Video": "", ":count Tours": "", "Tour snapshot": "", "Important information": "", "Not sure? You can cancel this reservation up to 24 hours in advance for a full refund.": "", "See less": "", "See details & photo": "", "Ready to jump back in?": "", "Earning Statistics": "", "No booking": "", "Your avatar": "", "PNG or JPG no bigger than 800px wide and tall.": "", "User Name": "", "Page become an expert": "", "Website": "", "Founded Time": "", ":from - :to of :total+ :agent available": "", "Company Agent at ": "", "Show less": "", "For Sale": "", "Professional Information": "", "Agency address": "", "Contact Form": "", "There are many variations of passages.": "", "Enter agent name": "", "Name (\r\n                                     a -> z )": "", "Name (\r\n                                     z -> a )": "", ":from - :to of :total+ :property available": "", ":from - :to of :total+ :agency available": "", "Agency Information": "", "Broker address": "", "Websites": "", "Member since": "", "View Listings": "", "All Agencies": "", "Enter agency name": "", "All Cities": "", "Name (a -> z)": "", "Name (z -> a)": "", "Link Map": "", "Form Title": "", "Form Sub Title": "", "Textarea": "", "Footer Content": "", "Phone Contact": "", "Currency:": "", "OR": "", "Continue Google": "", "Continue Facebook": "", "Continue Apple": "", "Not signed up? ": "", "Create an account.": "", "Email Address": "", "Password Confirm": "", "Create account": "", "Done": "", "Grid": "", "List": "", "Keep Yourself Up to Date": "", "Login / Register": "", "MANAGE LISTINGS": "", "Add New Property": "", "My Properties": "", "MANAGE ACCOUNT": "", "Welcome to": "", "New Account": "", "Number Items": "", "Button Title": "", "Button Link": "", "Location Blocks": "", "See All Properties": "", "View City": "", "See All Cities": "", "Share this post": "", "Previous Post": "", "Next Post": "", ":from - :to of :total+ properties available": "", ":from - :to of :total+ news available": "", "We are glad to see you again!": "", "Banner Image 1": "", "Banner Image 2": "", "Select Attributes": "", "Category Title": "", "Category Limit": "", "Select Categories": "", "Link Video Ember": "", "Select Properties": "", "List Counter": "", "Plus": "", "Class Wrapper": "", "Form Search": "", "Background Color": "", "List Categories": "", "Image Upload": "", "List Featured Properties": "", "Filter by category": "", "Show Type": "", "Both": "", "If left blank the button will be hidden": "", "Custom Class (optional)": "", "Background": "", "List Properties": "", "Bedroom": "", "Bath": "", "Sqft": "", "Floor Plans": "", "Bed": "", "Sidebar": "", "Map Search Layout": "", "Grid Cols": "", "Grid Style": "", "Search desc": "", "Search Background Image": "", "Sidebar detail page": "", "Vendor information": "", "Vendor contact": "", "Property search": "", "Property featured": "", "More Filter": "", "Price Range": "", "any": "", "Reset all filters": "", "Enter a property name or an address": "", "Enter Keyword": "", "Any Category": "", "Popular Searches": "", "Search products…": "", "sq ft": "", "View Details": "", "Enter Keywords": "", "Looking For": "", "Any Location": "", "View detail": "", "See All Categories": "", "FOR SALE": "", ":count bath": "", "Total Free Customer Care": "", "Nee Live Support?": "", "bed": "", "bath": "", "See All :total Photos": "", "Property Description": "", "Size:": "", "Property Showcase": "", "Related Properties": "", "Request Information": "", "Contact vendor": "", "Enter Your Messages": "", "Get More Information": "", "Contact Agent": "", ":number bed": "", ":number bath": "", "Features & Amenities": "", "Find your home": "", "All\r\n                        Type": "", "any bath": "", "any bed": "", "Any": "", "Is Sold": "", "Listing title": "", "Date Published": "", "Deletel": "", "Sign in with this account across the following sites.": "", "Go Back To Homepages": "", "Image Uploader 2": "", "Image Uploader 3": "", "Icon Class - get class in <a href=\"https://www.flaticon.com\" target=\"_blank\">https://www.flaticon.com</a>": "", "Title for list": "", "First item is main item": "", "Link to": "", "Block Plans": "", "Saving": "", "List Plan(s)": "", "Filter by Plan": "", "Block Teams": "", "List Team(s)": "", "Link": "", "Brands List": "", "Custom Class": "", "List Brand(s)": "", "Brand Logo": "", "Url": "", "Class (css)": "", "Button Name 2": "", "Button Url 2": "", "Margin": "", "Counter": "", "Button Name": "", "Button Url": "", "Block Download App": "", "List App(s) Download": "", "Link download": "", "Fun Fact": "", "Animate": "", "Icon Box": "", "Is Featured?": "", "Text color": "", "List Fact Item(s)": "", "Why Choose us": "", "Featured Text": "", "Featured Value": "", "List Text Item(s)": "", "Featured icon": "", "Billed Monthly": "", "Billed Yearly": "", "per month": "", "Join": "", "Password Confirm is required field": "", "Pricing Icon": "", "Filter: ": "", "Manage Account": "", "Membership Plans": "", "Save up to 20%": "", "Upload Profile Files": "", "Photos must be JPEG or PNG format and least 2048x768": "", "About me": "", "Social Information": "", "--Select Icon--": "", "Update Profile": "", "My Favorites": "", "No Wishlist": "", "Your trip": "", "About this boat": "", "Boat's Location": "", "Frequently asked questions": "", "/ per hour": "", "/ per day": "", "Explore other options": "", "Add to wishlist": "", "Check": "", "(:number Reviews)": "", "(:number Review)": "", "Layout Detail": "", "Seats": "", "Gear": "", "About this car": "", "Car's Location": "", "List Item(s) Contact": "", "Error 404 background image": "", "About this event": "", "Event Location": "", "All photo": "", "View in a map": "", "About this hotel": "", "Reserve a room": "", "Owner": "", "Enter Email": "", "Forgot Password": "", "Create an account": "", "Enter First Name": "", "Enter Last Name": "", "Enter Phone": "", "Enter Password": "", "I confirm that I have read and accepted the privacy policy": "", "Already have an account": "", "Information Contact": "", "Show on the list": "", "Sign in to your account": "", "Block subscribe settings": "", "Settings for block subscribe": "", "Subscribe title": "", "Subscribe sub title": "", "Subscribe Image": "", "Stories, tips, and guides": "", "Post navigation": "", "BY": "", "404 Page": "", "Go to home": "", "View Less": "", "Leave a review": "", "Required fields are marked": "", "About this rental": "", "Rental's Location": "", "List About": "", "Other Blocks": "", "List Item(s) Right": "", "Google Map Block": "", "Iframe Google Map": "", "Icon Image": "", "Icon Class - get class in <a href=\"https://fontawesome.com/v4/icons/\" target=\"_blank\">https://fontawesome.com/v4/icons/</a>": "", "Number Star": "", "Video Block": "", "Video Link": "", "About this tour": "", "Payment": "", "Fee:": "", "Includes": "", "Excludes": "", "Filter Search": "", "Other Settings": "", "Why Book With Us?": "", "Class icon": "", "Customer care available 24/7": "", "Title - Link info": "", "By continuing, you agree to the": "", "Terms and Conditions": "", "CONFIRM BOOKING": "", "Your Card Information": "", "Thank You. Your booking was submitted successfully!": "", "Your booking status is: :status": "", "Car Blocks": "", "Car: List Term Items": "", "Specifications": "", "Specifications List": "", "Specifications Desc": "", "Specifications name": "", "SAVE :text": "", "Pick Up Date": "", "Save for later": "", "View On Map": "", "Sends us a Message": "", "Link View on Map": "", "Footer Info Contact": "", "Logo Color": "", "Logo Text": "", "List Event by IDs": "", "Event Blocks": "", ":count '.$type->name)}}\">{{__(':count'.$type->name,['count'=>$inputValue??$minValue])}}</span>\r\n                        </span>\r\n\t\t\t\t\t@endforeach\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"dropdown-menu custom-select-dropdown\">\r\n\t\t\t@foreach($seatType as $type)\r\n\t\t\t\t<?php\r\n                $inputName = 'seat_type_'.$type->code;\r\n                $inputValue = $seatTypeGet[$type->code] ?? $minValue;\r\n                ;?>\r\n\t\t\t\r\n\t\t\t\t<div class=\"dropdown-item-row\">\r\n\t\t\t\t\t<div class=\"label\">{{__('Adults :type": "", "Show all": "", "Flight Details": "", "List Hotel by IDs": "", "Hotel Blocks": "", "Badge tag": "", "Eg: service VIP": "", "Brown": "", "Maroon": "", "Green": "", "Danger": "", "Info": "", "Dark": "", "Eg: Service VIP": "", "night": "", "Address-Description": "", "Select Your Room": "", "Room": "", "Mailing List": "", "Sign up for our mailing list to get latest updates and offers.": "", "Page navigation": "", "Sign in or Register": "", "First row 2 cards": "", "Style 3 cards/ row": "", "First row 3 cards": "", "Slide 4 cards/slider": "", "Style 5 cards/ row": "", "Location Name": "", "Location Desc": "", "Location Button Text": "", "Location Button Link": "", "Number Item (Default: 4)": "", "Unmissable Destinations": "", "Welcome to :name": "", "Top Experiences in :name": "", "Recent articles": "", "Read More Articles": "", "Company or title": "", "List Space by IDs": "", "Space Blocks": "", ":num :text": "", "List Brand Item(s)": "", "Breadcrumb Section": "", "Background Gradient overlay": "", "Grayish Blue": "", "Blue Light": "", "Orange": "", "Tab button Pills": "", "Tab button Boxed": "", "Tab button Shadow": "", "- Style 1: Background Image Uploader": "", "single form search": "", "- Style 1 : Image Uploader": "", "- Style 2, Style 3 : Icon Class": "", "Video Caption": "", "List Tour by IDs": "", "Tour Blocks": "", "Date From-To": "", "Min age": "", "Pickup": "", "Wifi available": "", "Max People": "", "Wifi Available": "", "Min Age:": "", "Pickup:": "", "$row->pickup": "", "Price Includes": "", "Price Excludes": "", "auth.failed": "", "The provided two factor authentication code was invalid.": "", "The provided password was incorrect.": "", "The provided two factor recovery code was invalid.": "", "auth.throttle": "", "The :attribute must be at least :length characters and contain at least one uppercase character.": "", "The :attribute must be at least :length characters and contain at least one number.": "", "The :attribute must be at least :length characters and contain at least one special character.": "", "The :attribute must be at least :length characters and contain at least one uppercase character and one number.": "", "The :attribute must be at least :length characters and contain at least one uppercase character and one special character.": "", "The :attribute must be at least :length characters and contain at least one uppercase character, one number, and one special character.": "", "The :attribute must be at least :length characters and contain at least one special character and one number.": "", "The :attribute must be at least :length characters.": "", "Reset Password Notification": "", "You are receiving this email because we received a password reset request for your account.": "", "This password reset link will expire in :count minutes.": "", "If you did not request a password reset, no further action is required.": "", "Please click the button below to verify your email address.": "", "If you did not create an account, no further action is required.": "", "Payment Required": "", "All rights reserved.": "", "Whoops!": "", "Hello!": "", "to": "", "of": "", "results": "", "Pagination Navigation": "", "Go to page :page": "", "There was an error on row :row. :message": "", "diff_now": "", "diff_yesterday": "", "diff_tomorrow": "", "diff_before_yesterday": "", "diff_after_tomorrow": "", "period_recurrences": "", "period_interval": "", "period_start_date": "", "period_end_date": "", "validation.phone": "", "installer_messages.final.finished": "", "installer_messages.environment.success": "", "installer_messages.environment.errors": "", "installer_messages.installed.success_log_message": "", "installer_messages.updater.log.success_message": "", "installer_messages.environment.wizard.tabs.environment": "", "installer_messages.environment.wizard.tabs.database": "", "installer_messages.environment.wizard.tabs.application": "", "installer_messages.environment.wizard.form.app_name_label": "", "installer_messages.environment.wizard.form.app_url_label": "", "installer_messages.environment.wizard.form.buttons.setup_application": "", "installer_messages.final.migration": "", "installer_messages.final.console": "", "installer_messages.final.env": "", "installer_messages.welcome.title": "", "There are two apples": ""}