<?php
/**
 * Created by PhpStorm.
 * User: dunglinh
 * Date: 6/9/19
 * Time: 00:01
 */

return [
    'item_url'=>'https://codecanyon.net/item/booking-core-ultimate-booking-system/24043972',
    'list_demo'=>[
        [
            'url'=>'https://sandbox.bookingcore.co',
            'name'=>'Homepage',
            'thumb'=>'img/demo/home.png',
        ],
        [
            'url'=>'https://sandbox.bookingcore.co/tour',
            'name'=>'Tour Search',
            'thumb'=>'img/demo/tour.png'
        ],
        [
            'url'=>'https://sandbox.bookingcore.co/tour/paris-vacation-travel',
            'name'=>'Single Tour',
            'thumb'=>'img/demo/tour_single.png'
        ],
        [
            'url'=>'https://sandbox.bookingcore.co/en/page/space',
            'name'=>'Home Space',
            'thumb'=>'img/demo/home-space.png',
        ],
        [
            'url'=>'https://sandbox.bookingcore.co/en/space',
            'name'=>'Space Search',
            'thumb'=>'img/demo/space-search.png'
        ],
        [
            'url'=>'https://sandbox.bookingcore.co/en/space/stay-greenwich-village',
            'name'=>'Single Space',
            'thumb'=>'img/demo/single-space.png'
        ],
        [
            'url'=>'https://sandbox.bookingcore.co/tour?_layout=map',
            'name'=>'Search Map',
            'thumb'=>'img/demo/tour_map.png',
            'class'=>'col-lg-8 col-md-12 col-sm-12'
        ],
        [
            'url'=>'https://sandbox.bookingcore.co/news',
            'name'=>'Blog List',
            'thumb'=>'img/demo/news.png'
        ],
//        [
//            'url'=>'https://sandbox.bookingcore.co/contact',
//            'name'=>'Contact Page',
//            'thumb'=>'img/demo/contact.png'
//        ],
    ],
    'exclusive_features'=>[
        [
            'name'=>'Laravel 5.8',
            'desc'=>'Based of newest version of Laravel. The best popular PHP framework',
            'thumb'=>'img/plugin/laravel.png'
        ],
        [
            'name'=>'Role Based Access Control',
            'desc'=>'Allow you add/modify roles to create many type of user',
            'thumb'=>'img/plugin/rbac.png'
        ],
        [
            'name'=>'Image Optimization',
            'desc'=>'Built-in library that auto compress your photo when uploading',
            'thumb'=>'img/plugin/image-compress.png'
        ],
        [
            'name'=>'Media Management',
            'desc'=>'Manager all your uploaded file',
            'thumb'=>'img/plugin/media.png'
        ],
        [
            'name'=>'Menu & Page Builder',
            'desc'=>'Easy customize with cleaning code and well documented',
            'thumb'=>'img/plugin/code.png'
        ],
        [
            'name'=>'Vendor System',
            'desc'=>'Allow you to build a booking marketplace system',
            'thumb'=>'img/plugin/vendor.png'
        ],
    ],
    'screenshots'=>[
        [
            'name'=>'Pricing <span>Rules</span>',
            'desc'=>'Various booking rules allow your customize the system.',
            'thumb'=>'img/feature/booking_rules.png'
        ],
        [
            'name'=>'Site <span>Settings</span>',
            'desc'=>'Simple, easy options Information management. Flexible options will help you design the system in your own way.',
            'thumb'=>'img/feature/theme_option.png'
        ],
        [
            'name'=>'Vendor <span>Dashboard</span>',
            'desc'=>'Simple, handy interface, easy to manage information, manage articles as well as manage revenue by Tour and Booking History with completed and pending status.',
            'thumb'=>'img/feature/tour_partner.png'
        ],
        /*[
            'name'=>'Smart <span>Search</span>',
            'desc'=>'The vendor can create and manage their listings on the frontend by using the partner dashboard.<br>
The interface is simple and clear, the vendor who does not need experience on the website can also post the most easily',
            'thumb'=>'img/feature/add_listing.png'
        ],*/
        [
            'name'=>'Template <span>Builder</span>',
            'desc'=>'Build your own page template with simple Template Builder.',
            'thumb'=>'img/feature/template-builder.png'
        ],
        [
            'name'=>'Menu <span>Builder</span>',
            'desc'=>'Build your own menu with simple menu Builder.',
            'thumb'=>'img/feature/menu-builder.png'
        ]
    ],
    'other_features'=>[
        [
            'name'=>'Optimized code',
            'desc'=>'A preeminent advantage that pleases all potential developers, can transform from a simple to the most complex, most versatile system.',
            'thumb'=>'img/other/optimize.png',
            'type'=>''
        ],
        [
            'name'=>'Booking Report',
            'desc'=>'Clear interface, detailed statistics chart, high logic will greatly support managers.',
            'thumb'=>'img/other/report.png',
            'type'=>''
        ],
        [
            'name'=>'Fully Responsive',
            'desc'=>'Perfect Responsive is the most effective way to win customers. All information, images are presented clearly, neatly and cleanly.',
            'thumb'=>'img/other/responsive.png',
            'type'=>''
        ],
        [
            'name'=>'Maximum speed rating',
            'desc'=>"One of Laravel's outstanding advantages is Speed, websites built on this platform never mind have to think about the problems of website speed optimization.",
            'thumb'=>'img/other/startup.png',
            'type'=>''
        ],
        [
            'name'=>'Retina Ready',
            'desc'=>"All of the graphics in our theme are of high resolution to ensure that your website looks crisp on modern displays.",
            'thumb'=>'img/other/retina.svg',
            'type'=>''
        ],
        [
            'name'=>'Make Review',
            'desc'=>"Make a review for all services, with some options, allows required booked or no need can make reviews.",
            'thumb'=>'img/other/review.svg',
            'type'=>''
        ],
        [
            'name'=>'Social Login',
            'desc'=>"Integrated by built-ins with Google, Facebook login. Difference register form normal user and Partner / Vendor user.",
            'thumb'=>'img/other/social_login.svg',
            'type'=>''
        ],
        [
            'name'=>'Support Location',
            'desc'=>"Support Location post type for build location structure by your way. Included lat & longtidule of Google Map.",
            'thumb'=>'img/other/location.svg',
            'type'=>''
        ],
        [
            'name'=>'Translate tool built-in',
            'desc'=>"Tool compiled the built-in language, customers just follow the Document and personalize the text to suit their intended use.",
            'thumb'=>'img/other/language.png',
            'type'=>''
        ],
        [
            'name'=>'Lazy Load',
            'desc'=>"Increase performance of your website with lazy load image.",
            'thumb'=>'img/other/speedometer.png',
            'type'=>''
        ],
        [
            'name'=>'And Much More',
            'thumb'=>'img/other/more.svg',
            'type'=>'more'
        ],
    ]
];
